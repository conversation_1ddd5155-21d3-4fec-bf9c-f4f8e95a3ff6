# Server Configuration
# NODE_ENV can be 'development', 'production', or 'local'
# Use 'local' for localhost development (SSL disabled)
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_URL=postgres://postgres:Admin123@localhost:5432/wtd_local

# JWT Configuration
JWT_SECRET=your_jwt_secret_key

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=your_aws_region
AWS_S3_BUCKET=your_s3_bucket_name

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>

# Logging
LOG_LEVEL=info

# Swagger Documentation
SWAGGER_DOCS_USER=api_docs
SWAGGER_DOCS_PASSWORD=ApiDocs@2025
