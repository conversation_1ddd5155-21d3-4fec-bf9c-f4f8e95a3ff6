/**
 * Main Application Entry Point
 */
require('module-alias/register');
const { execSync } = require('child_process');
const dotenv = require('dotenv');

// Load environment variables from .env file first
dotenv.config();

// Import Logger first so we can use it for logging
const Logger = require('./src/utils/logger.utils');

// Use NODE_ENV from .env file or set default if not available
const envMode = process.env.NODE_ENV || 'development';

// Log the environment being used
Logger.info(`Using environment: ${envMode}`);

// Log appropriate message based on environment
switch (envMode) {
  case 'local':
    Logger.info('Running in local mode (SSL disabled)');
    break;
  case 'development':
    Logger.info('Running in development mode');
    break;
  case 'production':
    Logger.info('Running in production mode');
    break;
  default:
    Logger.info(`Running in ${envMode} mode`);
    break;
}

const app = require('./src/app');
const { APP } = require('@config/app.config');

// Server instance
let server;

// Handle graceful shutdown
process.on('SIGINT', () => {
  Logger.info('Received SIGINT. Shutting down gracefully...');
  if (server) {
    server.close(() => {
      Logger.info('Server closed');
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
});

process.on('SIGTERM', () => {
  Logger.info('Received SIGTERM. Shutting down gracefully...');
  if (server) {
    server.close(() => {
      Logger.info('Server closed');
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
});

// Start the server if this file is run directly
if (require.main === module) {
  try {
    // Configure SSL settings based on environment
    const sslEnv =
      process.env.NODE_ENV === 'local'
        ? {}
        : { NODE_TLS_REJECT_UNAUTHORIZED: '0' };

    // Run database migrations for all environments
    Logger.info(
      `Running database migrations in ${process.env.NODE_ENV} environment...`
    );
    try {
      execSync('npx sequelize-cli db:migrate', {
        stdio: 'inherit',
        env: { ...process.env, ...sslEnv },
      });
      Logger.success('Database migrations completed successfully');
    } catch (error) {
      Logger.error('Error running database migrations:', error.message);
      Logger.warn(
        'Continuing without migrations. Some features may not work correctly.'
      );
    }

    // Run database seeds for all environments
    Logger.info(
      `Running database seeds in ${process.env.NODE_ENV} environment...`
    );
    try {
      execSync('npx sequelize-cli db:seed:all', {
        stdio: 'inherit',
        env: { ...process.env, ...sslEnv },
      });
      Logger.success('Database seeds completed successfully');
    } catch (error) {
      Logger.error('Error running database seeds:', error.message);
      Logger.warn(
        'Continuing without seeds. Some features may not work correctly.'
      );
    }

    // Start the server
    server = app.listen(APP.port, () => {
      Logger.success('Server started', {
        url: `http://localhost:${APP.port}`,
        environment: APP.env,
        apiDocs: {
          admin: `http://localhost:${APP.port}/api-docs/admin`,
          user: `http://localhost:${APP.port}/api-docs/user`,
        },
      });
    });

    Logger.info('Server is now running. Press Ctrl+C to stop.');
  } catch (error) {
    Logger.error('Error starting server:', error);
    process.exit(1);
  }
}

// Export both the app and server (if created)
module.exports = { app, server };
