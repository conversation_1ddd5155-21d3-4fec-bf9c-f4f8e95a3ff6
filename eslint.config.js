module.exports = {
  files: ['**/*.js'],
  plugins: {
    prettier: require('eslint-plugin-prettier'),
    import: require('eslint-plugin-import'),
  },
  languageOptions: {
    ecmaVersion: 12,
  },
  ignores: [
    'api-docs/*',
    'node_modules/*',
    'seeders/*',
    '.history',
    '.idea',
    '.eslintcache',
    '.env',
  ],
  rules: {
    'no-var': 'warn',
    'linebreak-style': 'off',
    quotes: [
      'error',
      'single',
      { avoidEscape: true, allowTemplateLiterals: true },
    ],
    'block-spacing': ['error', 'always'],
    'no-mixed-spaces-and-tabs': 'error',
    'comma-spacing': ['error', { before: false, after: true }],
    'comma-style': ['error', 'last'],
    'no-unused-expressions': [
      'error',
      {
        allowShortCircuit: true,
        allowTernary: true,
        allowTaggedTemplates: true,
      },
    ],
    'no-unused-vars': [
      'error',
      {
        args: 'none',
        caughtErrors: 'none',
        ignoreRestSiblings: true,
        vars: 'all',
      },
    ],
    'no-import-assign': 'error',
    'no-console': 0,
    'global-require': 0,
    'no-param-reassign': 0,
    'consistent-return': 0,
    camelcase: 1,
    'no-multiple-empty-lines': 'error',
    'func-names': 0,
    'no-plusplus': 0,
    'no-return-await': 0,
    'no-return-assign': 0,
    'import/no-dynamic-require': 0,
    'default-param-last': 0,
    radix: 0,
    'no-await-in-loop': 0,
    'prettier/prettier': ['error'],
  },
};
