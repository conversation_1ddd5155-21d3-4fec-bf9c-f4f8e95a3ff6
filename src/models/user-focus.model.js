/**
 * UserFocus Model
 * Junction table for User and Focus many-to-many relationship
 */
const { Model, DataTypes } = require('sequelize');

class UserFocus extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        focusId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Focus',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'UserFocus',
        tableName: 'UserFocus',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['userId', 'focusId'],
            name: 'user_focus_unique',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to User
    UserFocus.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });

    // Belongs to Focus
    UserFocus.belongsTo(models.Focus, {
      foreignKey: 'focusId',
      as: 'focus',
    });
  }
}

module.exports = UserFocus;
