'use strict';

const { Model, DataTypes } = require('sequelize');

/**
 * WtdIceBreaker Model
 */
class WtdIceBreaker extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
        },
        teachSubject: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        contentIn3Words: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        inspiration: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        funFact: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        magicButton: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        resourcesEmojis: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        themeSong: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        impactTeacher: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        isPrivate: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
      },
      {
        sequelize,
        modelName: 'WtdIceBreaker',
        tableName: 'WtdIceBreaker',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['userId'],
            name: 'wtd_ice_breaker_user_id_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    WtdIceBreaker.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
  }

  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = WtdIceBreaker;
