/**
 * ExperienceWeekInsightFocus Model
 * Junction table for ExperienceWeekInsight and Focus many-to-many relationship
 */
const { Model, DataTypes } = require('sequelize');

class ExperienceWeekInsightFocus extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        experienceWeekInsightId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'ExperienceWeekInsight',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        focusId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Focus',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'ExperienceWeekInsightFocus',
        tableName: 'ExperienceWeekInsightFocus',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['experienceWeekInsightId', 'focusId'],
            name: 'experience_week_insight_focus_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
    // Associations are defined in the main models
  }
}

module.exports = ExperienceWeekInsightFocus;
