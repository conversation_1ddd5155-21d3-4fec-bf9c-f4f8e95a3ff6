/**
 * OnboardingConfig Model
 * Represents configuration for onboarding screens with image, title and description
 */
const { Model, DataTypes } = require('sequelize');

class OnboardingConfig extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        imageUrl: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        title: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'OnboardingConfig',
        tableName: 'OnboardingConfig',
        timestamps: true,
      }
    );
  }
}

module.exports = OnboardingConfig;
