/**
 * WTD Category Repository
 *
 * Handles data access operations for the WTD Category model
 */
const { ApiException } = require('@utils/exception.utils');
const WtdCategory = require('@models/wtd-category.model');
const { WTD_CATEGORY } = require('@utils/messages.utils');
const { Op } = require('sequelize');

class WtdCategoryRepository {
  /**
   * Find all WTD categories with pagination and optional search
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for name
   * @returns {Promise<Object>} Object containing categories and pagination info
   */
  async findAll({ page = 1, limit = 10, search = '' } = {}) {
    try {
      // Build where clause for search
      const whereClause = {};
      if (search) {
        whereClause.name = {
          [Op.iLike]: `%${search}%`,
        };
      }

      // Calculate offset
      const offset = (page - 1) * limit;

      // Get total count
      const count = await WtdCategory.count({ where: whereClause });

      // Get paginated results
      const categories = await WtdCategory.findAll({
        where: whereClause,
        order: [['name', 'ASC']],
        limit,
        offset,
      });

      // Calculate pagination metadata
      const totalPages = Math.ceil(count / limit);
      const hasNext = page < totalPages;
      const hasPrevious = page > 1;

      return {
        categories,
        pagination: {
          total: count,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrevious,
        },
      };
    } catch (error) {
      console.error('Error in findAll repository:', error);
      throw error;
    }
  }

  /**
   * Find WTD category by ID
   * @param {string} id - WTD category UUID
   * @returns {Promise<WtdCategory>} WTD category instance
   * @throws {ApiException} If WTD category not found
   */
  async findById(id) {
    try {
      const category = await WtdCategory.findByPk(id);

      if (!category) {
        throw new ApiException(404, WTD_CATEGORY.NOT_FOUND);
      }

      return category;
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }

  /**
   * Find WTD category by name (case-insensitive)
   * @param {string} name - WTD category name
   * @returns {Promise<WtdCategory|null>} WTD category instance or null if not found
   */
  async findByName(name) {
    try {
      return await WtdCategory.findOne({
        where: {
          name: {
            [Op.iLike]: name,
          },
        },
      });
    } catch (error) {
      console.error('Error in findByName repository:', error);
      throw error;
    }
  }

  /**
   * Create a new WTD category
   * @param {Object} data - WTD category data
   * @returns {Promise<WtdCategory>} Created WTD category
   * @throws {ApiException} If name already exists
   */
  async create(data) {
    try {
      // Check if a category with this name already exists (case-insensitive)
      const existingCategory = await this.findByName(data.name);

      if (existingCategory) {
        throw new ApiException(409, WTD_CATEGORY.ALREADY_EXISTS);
      }

      return await WtdCategory.create(data);
    } catch (error) {
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Update WTD category
   * @param {string} id - WTD category UUID
   * @param {Object} data - Data to update
   * @returns {Promise<WtdCategory>} Updated WTD category
   * @throws {ApiException} If WTD category not found or name already exists
   */
  async update(id, data) {
    try {
      const category = await this.findById(id);

      // If name is being updated, check if it already exists
      if (
        data.name &&
        data.name.toLowerCase() !== category.name.toLowerCase()
      ) {
        const existingCategory = await this.findByName(data.name);

        if (existingCategory) {
          throw new ApiException(409, WTD_CATEGORY.ALREADY_EXISTS);
        }
      }

      await category.update(data);
      return category;
    } catch (error) {
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Delete WTD category
   * @param {string} id - WTD category UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If WTD category not found
   */
  async delete(id) {
    try {
      const category = await this.findById(id);
      await category.destroy();
      return true;
    } catch (error) {
      console.error('Error in delete repository:', error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new WtdCategoryRepository();
