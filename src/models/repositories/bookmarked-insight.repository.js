/**
 * BookmarkedInsight Repository
 *
 * Handles data access operations for the BookmarkedInsight model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { INSIGHT } = require('@utils/messages.utils');
const { Op } = require('sequelize');

class BookmarkedInsightRepository {
  /**
   * Toggle bookmark status for an insight
   * @param {string} userId - User UUID
   * @param {string} insightId - Insight UUID
   * @returns {Promise<Object>} Object with isBookmarked status
   * @throws {ApiException} If insight not found
   */
  async toggleBookmark(userId, insightId) {
    try {
      // Get models
      const BookmarkedInsight = databaseService.getBookmarkedInsightModel();
      const Insight = databaseService.getInsightModel();

      // Check if insight exists
      const insight = await Insight.findByPk(insightId);
      if (!insight) {
        throw new ApiException(404, INSIGHT.NOT_FOUND);
      }

      // Check if already bookmarked
      const existingBookmark = await BookmarkedInsight.findOne({
        where: {
          userId,
          insightId,
        },
      });

      // If already bookmarked, remove it
      if (existingBookmark) {
        await existingBookmark.destroy();
        return { isBookmarked: false };
      }

      // Otherwise, create a new bookmark
      await BookmarkedInsight.create({
        userId,
        insightId,
      });

      return { isBookmarked: true };
    } catch (error) {
      console.error('Error in toggleBookmark repository:', error);
      throw error;
    }
  }

  /**
   * Check if an insight is bookmarked by a user
   * @param {string} userId - User UUID
   * @param {string} insightId - Insight UUID
   * @returns {Promise<boolean>} True if bookmarked
   */
  async isBookmarked(userId, insightId) {
    try {
      const BookmarkedInsight = databaseService.getBookmarkedInsightModel();

      const bookmark = await BookmarkedInsight.findOne({
        where: {
          userId,
          insightId,
        },
      });

      return !!bookmark;
    } catch (error) {
      console.error('Error in isBookmarked repository:', error);
      throw error;
    }
  }

  /**
   * Get all bookmarked insights for a user
   * @param {string} userId - User UUID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @returns {Promise<Object>} Object containing insights and pagination info
   */
  async getBookmarkedInsights(userId, { page = 1, limit = 10 } = {}) {
    try {
      // Calculate offset
      const offset = (page - 1) * limit;

      // Get User model to access bookmarked insights
      const User = databaseService.getUserModel();

      // Use the User model to get bookmarked insights through the association
      const user = await User.findByPk(userId);
      if (!user) {
        throw new ApiException(404, 'User not found');
      }

      // Get the total count of bookmarks for pagination
      const count = await user.countBookmarkedInsights();

      // Get bookmarked insights with pagination
      const bookmarkedInsights = await user.getBookmarkedInsights({
        limit,
        offset,
        attributes: {
          exclude: [
            'pdCategoryId',
            'createdBy',
            'status',
            'reviewedBy',
            'reviewedAt',
            'rejectionReason',
          ],
          include: [
            // Add subquery for likes count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id")`
                ),
              'likesCount',
            ],
            // Add subquery for implementations count
            [
              databaseService
                .getSequelize()
                .literal(
                  `(SELECT COUNT(*)::INTEGER FROM "ImplementedInsight" WHERE "ImplementedInsight"."insightId" = "Insight"."id")`
                ),
              'implementationCount',
            ],
          ],
        },
        include: [
          {
            association: 'creator',
            attributes: ['id', 'firstName', 'lastName', 'profilePic'],
          },
          {
            association: 'pdCategory',
            attributes: ['id', 'name'],
          },
          {
            association: 'focus',
            attributes: ['id', 'name'],
            through: { attributes: [] },
          },
          {
            association: 'wtdCategories',
            attributes: ['id', 'name'],
            through: { attributes: [] },
          },
        ],
        order: [['createdAt', 'DESC']],
      });

      // Get all likes and implementations for this user and the returned insights
      const LikedInsight = databaseService.getLikedInsightModel();
      const ImplementedInsight = databaseService.getImplementedInsightModel();
      const insightIds = bookmarkedInsights.map((insight) => insight.id);

      // Get all likes for this user and the returned insights
      const likes = await LikedInsight.findAll({
        where: {
          userId,
          insightId: {
            [Op.in]: insightIds,
          },
        },
        raw: true,
      });

      // Get all implementations for this user and the returned insights
      const implementations = await ImplementedInsight.findAll({
        where: {
          userId,
          insightId: {
            [Op.in]: insightIds,
          },
        },
        raw: true,
      });

      // Create sets for faster lookup
      const likedInsightIds = new Set(likes.map((l) => l.insightId));
      const implementedInsightIds = new Set(
        implementations.map((i) => i.insightId)
      );

      // Add isBookmarked flag to all insights (they are all bookmarked)
      // and add isLiked and isImplemented flags based on the user's interactions
      const insights = bookmarkedInsights.map((insight) => {
        const { BookmarkedInsight, ...insightData } = insight.toJSON();
        return {
          ...insightData,
          isBookmarked: true,
          isLiked: likedInsightIds.has(insight.id),
          isImplemented: implementedInsightIds.has(insight.id),
        };
      });

      // Calculate pagination info
      const totalPages = Math.ceil(count / limit);
      const hasNext = page < totalPages;
      const hasPrevious = page > 1;

      return {
        insights,
        pagination: {
          total: count,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrevious,
        },
      };
    } catch (error) {
      console.error('Error in getBookmarkedInsights repository:', error);
      throw error;
    }
  }
}

module.exports = new BookmarkedInsightRepository();
