/**
 * User Repository
 *
 * Handles data access operations for the User model
 */
const { ApiException } = require('@utils/exception.utils');
const { USER, AUTH } = require('@utils/messages.utils');
const { UserType } = require('@utils/enums.utils');
const databaseService = require('@config/database.config');
const { HttpStatus } = require('@utils/enums.utils');
const { VALIDATION } = require('@utils/messages.utils');
const { Op } = require('sequelize');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      User: databaseService.getUserModel(),
      Focus: databaseService.getFocusModel(),
      UserFocus: databaseService.getUserFocusModel(),
      Contribution: databaseService.getContributionModel(),
      Insight: databaseService.getInsightModel(),
      Experience: databaseService.getExperienceModel(),
      WtdIceBreaker: databaseService.getWtdIceBreakerModel(),
    };
  }
}

/**
 * Repository for user validation operations
 */
class UserValidationRepository extends BaseRepository {
  async validateFocusIds(focusIds, transaction) {
    if (focusIds?.length > 0) {
      const validFocuses = await this.models.Focus.findAll({
        where: {
          id: {
            [Op.in]: focusIds,
          },
        },
        attributes: ['id'],
        transaction,
        raw: true,
      });

      const validFocusIdSet = new Set(validFocuses.map((f) => f.id));
      const invalidFocusIds = focusIds.filter((id) => !validFocusIdSet.has(id));

      if (invalidFocusIds.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          VALIDATION.INVALID_FOCUS_ID
        );
      }
    }
  }
}

/**
 * Repository for user update operations
 */
class UserUpdateRepository extends BaseRepository {
  async updateUserFields(user, updateData, transaction) {
    const {
      credential: {
        companyName,
        website,
        state,
        country,
        description,
        isPrivate,
      } = {},
      departmentImpact: { isPrivate: isDepartmentPrivate } = {},
      ...userData
    } = updateData;

    const updateFields = {
      ...userData,
      companyName,
      website,
      state,
      country,
      description,
      isPrivate,
      isDepartmentPrivate,
    };

    return user.update(updateFields, { transaction });
  }

  async upsertIceBreaker(userId, iceBreakers, transaction) {
    if (!iceBreakers) return null;

    const iceBreakerData = {
      userId,
      teachSubject: iceBreakers.teachSubject,
      contentIn3Words: iceBreakers.contentIn3Words,
      inspiration: iceBreakers.inspiration,
      funFact: iceBreakers.funFact,
      magicButton: iceBreakers.magicButton,
      resourcesEmojis: iceBreakers.resourcesEmojis,
      themeSong: iceBreakers.themeSong,
      impactTeacher: iceBreakers.impactTeacher,
      isPrivate: iceBreakers.isPrivate,
    };

    return this.models.WtdIceBreaker.upsert(iceBreakerData, {
      transaction,
      conflictFields: ['userId'],
    });
  }

  async updateUserFocus(userId, focusIds, transaction) {
    if (focusIds === undefined) return null;

    // Remove existing focus associations
    await this.models.UserFocus.destroy({
      where: { userId },
      transaction,
    });

    // Create new focus associations if provided
    if (focusIds?.length > 0) {
      const focusAssociations = focusIds.map((focusId) => ({
        userId,
        focusId,
      }));

      return this.models.UserFocus.bulkCreate(focusAssociations, {
        transaction,
        ignoreDuplicates: true,
      });
    }

    return null;
  }
}

/**
 * User Helper class for complex operations
 */
class UserHelper extends BaseRepository {
  async createFocusAssociations(userId, focusIds, transaction) {
    if (focusIds?.length > 0) {
      const focusRecords = focusIds.map((focusId) => ({
        userId,
        focusId,
      }));
      await this.models.UserFocus.bulkCreate(focusRecords, {
        transaction,
        ignoreDuplicates: true,
      });
    }
  }

  async getProfileData(user) {
    const [contributionsCount, experiencesCreatedCount] = await Promise.all([
      this.getContributionsCount(user.id),
      this.getExperiencesCreatedCount(user.id),
    ]);

    return {
      id: user.id,
      profilePic: user.profilePic,
      firstName: user.firstName,
      lastName: user.lastName,
      userType: user.userType,
      currentMilestone: null,
      credential: {
        companyName: user.companyName,
        website: user.website,
        focus: user.focuses,
        state: user.state,
        country: user.country,
        description: user.description,
        isPrivate: user.isPrivate,
      },
      departmentImpact: {
        currentMilestone: null,
        contributions: contributionsCount,
        followers: 0, // Currently hardcoded to 0 as per requirements
        insightsShared: 0, // Currently hardcoded to 0 as per requirements
        experiencesCreated: experiencesCreatedCount,
        isPrivate: user.isDepartmentPrivate,
      },
      iceBreakers: user.iceBreaker,
    };
  }

  async getContributionsCount(userId) {
    try {
      return await this.models.Contribution.count({
        where: { contributedBy: userId },
      });
    } catch (error) {
      console.error('Error in getContributionsCount:', error);
      throw error;
    }
  }

  async getExperiencesCreatedCount(userId) {
    try {
      return await this.models.Experience.count({
        where: { createdBy: userId },
      });
    } catch (error) {
      console.error('Error in getExperiencesCreatedCount:', error);
      throw error;
    }
  }
}

/**
 * Main User Repository class
 */
class UserRepository extends BaseRepository {
  constructor() {
    super();
    this.validationRepository = new UserValidationRepository();
    this.updateRepository = new UserUpdateRepository();
    this.helper = new UserHelper();
  }

  /**
   * Get user profile includes for queries
   * @returns {Array} Array of include objects
   */
  _getUserProfileIncludes() {
    return [
      {
        association: 'focuses',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
      {
        association: 'iceBreaker',
        attributes: [
          'teachSubject',
          'contentIn3Words',
          'inspiration',
          'funFact',
          'magicButton',
          'resourcesEmojis',
          'themeSong',
          'impactTeacher',
          'isPrivate',
        ],
      },
    ];
  }

  /**
   * Find user by ID
   * @param {string} id - User UUID
   * @param {Object} options - Query options
   * @returns {Promise<User>} User instance
   * @throws {ApiException} If user not found
   */
  async findById(id, options = {}) {
    try {
      const user = await this.models.User.findByPk(id, options);

      if (!user) {
        throw new ApiException(404, USER.USER_NOT_FOUND);
      }

      return user;
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }

  /**
   * Find user by email
   * @param {string} email - User email
   * @returns {Promise<User|null>} User instance or null
   */
  async findByEmail(email) {
    try {
      return await this.models.User.findOne({ where: { email } });
    } catch (error) {
      console.error('Error in findByEmail repository:', error);
      throw error;
    }
  }

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<User>} Created user
   * @throws {ApiException} If email already exists
   */
  async create(userData) {
    try {
      const existingUser = await this.findByEmail(userData.email);

      if (existingUser) {
        throw new ApiException(409, AUTH.EMAIL_ALREADY_EXISTS);
      }

      return await this.models.User.create(userData);
    } catch (error) {
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Update user
   * @param {string} id - User UUID
   * @param {Object} updateData - Data to update
   * @returns {Promise<User>} Updated user
   * @throws {ApiException} If user not found
   */
  async update(id, updateData) {
    try {
      const user = await this.findById(id);
      await user.update(updateData);
      return user;
    } catch (error) {
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Delete user
   * @param {string} id - User UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If user not found
   */
  async delete(id) {
    try {
      const user = await this.findById(id);
      await user.destroy();
      return true;
    } catch (error) {
      console.error('Error in delete repository:', error);
      throw error;
    }
  }

  /**
   * Change user password
   * @param {string} id - User UUID
   * @param {string} newPassword - New password
   * @returns {Promise<User>} Updated user
   * @throws {ApiException} If user not found
   */
  async changePassword(id, newPassword) {
    try {
      const user = await this.models.User.findByPk(id);

      if (!user) {
        throw new ApiException(404, USER.USER_NOT_FOUND);
      }

      user.password = newPassword;
      await user.save();
      return user;
    } catch (error) {
      console.error('Error in changePassword repository:', error);
      throw error;
    }
  }

  /**
   * Create user with focus associations
   * @param {Object} userData - User data including focus array
   * @returns {Promise<Object>} Created user with focus associations
   */
  async createUserWithFocus(userData) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      // Validate all focus IDs exist if focus array is provided
      await this.validationRepository.validateFocusIds(
        userData.focus,
        transaction
      );

      // Create user with modified data for PROVIDER_PLUS
      const user = await this.create(
        {
          ...userData,
          position:
            userData.userType === UserType.PROVIDER_PLUS
              ? null
              : userData.position,
          companyName:
            userData.userType === UserType.PROVIDER_PLUS
              ? userData.companyName
              : null,
        },
        { transaction }
      );

      // Create focus associations if focus array exists
      await this.helper.createFocusAssociations(
        user.id,
        userData.focus,
        transaction
      );

      // Commit the transaction
      await transaction.commit();

      // Return user without focus associations
      return this.findById(user.id);
    } catch (error) {
      // Rollback the transaction on error
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get user profile data with all necessary associations and counts
   * @param {string} userId - User UUID
   * @returns {Promise<Object>} Formatted user profile data
   */
  async getUserProfileData(userId) {
    try {
      const user = await this.findById(userId, {
        include: this._getUserProfileIncludes(),
      });

      return await this.helper.getProfileData(user);
    } catch (error) {
      console.error('Error in getUserProfileData repository:', error);
      throw error;
    }
  }

  /**
   * Update user profile with optimized operations
   * @param {string} userId - User UUID
   * @param {Object} updateData - Profile update data
   * @returns {Promise<Object>} Updated user profile data
   */
  async updateProfile(userId, updateData) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      // Extract focus from updateData for separate handling
      const {
        credential: { focus, ...credentialFields } = {},
        iceBreakers,
        ...restUpdateData
      } = updateData;

      // Reconstruct updateData with credential fields but without focus
      const updateDataForFields = {
        ...restUpdateData,
        credential: credentialFields,
      };

      // Validate focus IDs if provided
      if (focus !== undefined) {
        await this.validationRepository.validateFocusIds(focus, transaction);
      }

      // Get user for update
      const user = await this.findById(userId, { transaction });

      // Prepare parallel update operations
      const updateOperations = [];

      // 1. Update user fields
      updateOperations.push(
        this.updateRepository.updateUserFields(
          user,
          updateDataForFields,
          transaction
        )
      );

      // 2. Upsert ice breaker if provided
      if (iceBreakers) {
        updateOperations.push(
          this.updateRepository.upsertIceBreaker(
            userId,
            iceBreakers,
            transaction
          )
        );
      }

      // 3. Update user focus if provided
      if (focus !== undefined) {
        updateOperations.push(
          this.updateRepository.updateUserFocus(userId, focus, transaction)
        );
      }

      // Execute all updates in parallel
      await Promise.all(updateOperations.filter(Boolean));
      await transaction.commit();

      // Return updated user profile data
      return this.getUserProfileData(userId);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new UserRepository();
