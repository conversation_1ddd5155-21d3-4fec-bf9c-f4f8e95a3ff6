/**
 * ExperienceWeekProgress Repository
 *
 * Handles data access operations for the ExperienceWeekProgress model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const {
  HttpStatus,
  ExperienceWeekProgressStatus,
} = require('@utils/enums.utils');
const { EXPERIENCE_WEEK_PROGRESS } = require('@utils/messages.utils');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      ExperienceWeekProgress: databaseService.getExperienceWeekProgressModel(),
      ExperienceEnrollment: databaseService.getExperienceEnrollmentModel(),
      ExperienceWeek: databaseService.getExperienceWeekModel(),
    };
  }

  _getProgressAttributes() {
    return {
      exclude: ['enrollmentId', 'weekNumber'],
    };
  }

  _getProgressIncludes() {
    return [
      {
        model: this.models.ExperienceWeek,
        as: 'experienceWeek',
        attributes: ['id', 'weekNumber', 'title', 'weeklyWhy'],
      },
    ];
  }
}

/**
 * Helper class for Experience Week Progress operations
 */
class ExperienceWeekProgressHelper extends BaseRepository {
  /**
   * Enrich progress with additional data
   * @param {Object} progress - Progress object
   * @returns {Promise<Object>} Enriched progress
   */
  async enrichProgress(progress) {
    if (!progress) return null;
    return progress.toJSON();
  }

  /**
   * Enrich multiple progress records with additional data
   * @param {Array<Object>} progressRecords - Array of progress objects
   * @returns {Promise<Array<Object>>} Array of enriched progress records
   */
  async enrichProgressRecords(progressRecords) {
    if (!progressRecords?.length) return [];
    return Promise.all(
      progressRecords.map((progress) => this.enrichProgress(progress))
    );
  }

  /**
   * Calculate completion statistics for progress records
   * @param {Array<Object>} progressRecords - Array of progress records
   * @returns {Object} Completion statistics
   */
  calculateCompletionStats(progressRecords) {
    const totalWeeks = progressRecords.length;
    const completedWeeks = progressRecords.filter(
      (p) => p.status === ExperienceWeekProgressStatus.COMPLETED
    ).length;

    const currentWeek =
      progressRecords.find(
        (p) => p.status === ExperienceWeekProgressStatus.PENDING
      )?.weekNumber || completedWeeks + 1;

    return {
      totalWeeks,
      completedWeeks,
      currentWeek: currentWeek > totalWeeks ? totalWeeks : currentWeek,
      progressPercentage:
        totalWeeks > 0 ? Math.round((completedWeeks / totalWeeks) * 100) : 0,
      isCompleted: completedWeeks === totalWeeks && totalWeeks > 0,
    };
  }
}

/**
 * Main Experience Week Progress Repository class
 */
class ExperienceWeekProgressRepository extends BaseRepository {
  constructor() {
    super();
    this.helper = new ExperienceWeekProgressHelper();
  }

  /**
   * Find progress by ID
   * @param {string} id - Progress ID
   * @param {Object} options - Query options
   * @param {Object} options.transaction - Database transaction
   * @returns {Promise<Object>} Progress object
   * @throws {ApiException} If progress not found
   */
  async findById(id, options = {}) {
    try {
      const progress = await this.models.ExperienceWeekProgress.findByPk(id, {
        attributes: this._getProgressAttributes(),
        include: this._getProgressIncludes(),
        transaction: options.transaction,
      });

      if (!progress) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_WEEK_PROGRESS.NOT_FOUND
        );
      }

      return await this.helper.enrichProgress(progress);
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }

  /**
   * Find progress by enrollment and week
   * @param {string} enrollmentId - Enrollment ID
   * @param {number} weekNumber - Week number
   * @param {Object} options - Query options
   * @param {Object} options.transaction - Database transaction
   * @returns {Promise<Object>} Progress object
   */
  async findByEnrollmentAndWeek(enrollmentId, weekNumber, options = {}) {
    try {
      const progress = await this.models.ExperienceWeekProgress.findOne({
        where: { enrollmentId, weekNumber },
        attributes: this._getProgressAttributes(),
        include: this._getProgressIncludes(),
        transaction: options.transaction,
      });

      return await this.helper.enrichProgress(progress);
    } catch (error) {
      console.error('Error in findByEnrollmentAndWeek repository:', error);
      throw error;
    }
  }

  /**
   * Find all progress for an enrollment
   * @param {string} enrollmentId - Enrollment ID
   * @param {Object} options - Query options
   * @param {Object} options.transaction - Database transaction
   * @returns {Promise<Array>} Array of progress objects
   */
  async findByEnrollment(enrollmentId, options = {}) {
    try {
      const progressRecords = await this.models.ExperienceWeekProgress.findAll({
        where: { enrollmentId },
        attributes: this._getProgressAttributes(),
        include: this._getProgressIncludes(),
        order: [['weekNumber', 'ASC']],
        transaction: options.transaction,
      });

      return await this.helper.enrichProgressRecords(progressRecords);
    } catch (error) {
      console.error('Error in findByEnrollment repository:', error);
      throw error;
    }
  }

  /**
   * Create progress record
   * @param {Object} data - Progress data
   * @param {Object} options - Query options
   * @param {Object} options.transaction - Database transaction
   * @returns {Promise<Object>} Created progress
   */
  async create(data, options = {}) {
    try {
      const progress = await this.models.ExperienceWeekProgress.create(data, {
        attributes: this._getProgressAttributes(),
        include: this._getProgressIncludes(),
        transaction: options.transaction,
      });

      return await this.helper.enrichProgress(progress);
    } catch (error) {
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Create multiple progress records
   * @param {Array} progressData - Array of progress data
   * @param {Object} options - Query options
   * @param {Object} options.transaction - Database transaction
   * @returns {Promise<Array>} Created progress records
   */
  async bulkCreate(progressData, options = {}) {
    try {
      const progressRecords =
        await this.models.ExperienceWeekProgress.bulkCreate(progressData, {
          transaction: options.transaction,
        });

      return await this.helper.enrichProgressRecords(progressRecords);
    } catch (error) {
      console.error('Error in bulkCreate repository:', error);
      throw error;
    }
  }

  /**
   * Update progress
   * @param {string} id - Progress ID
   * @param {Object} data - Update data
   * @param {Object} options - Query options
   * @param {Object} options.transaction - Database transaction
   * @returns {Promise<Object>} Updated progress
   * @throws {ApiException} If progress not found
   */
  async update(id, data, options = {}) {
    try {
      const progress = await this.findById(id, options);
      if (!progress) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_WEEK_PROGRESS.NOT_FOUND
        );
      }

      const updatedProgress = await progress.update(data, {
        transaction: options.transaction,
      });

      return await this.helper.enrichProgress(updatedProgress);
    } catch (error) {
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Mark week as completed
   * @param {string} enrollmentId - Enrollment ID
   * @param {number} weekNumber - Week number
   * @param {Object} options - Query options
   * @param {Object} options.transaction - Database transaction
   * @returns {Promise<Object>} Updated progress
   * @throws {ApiException} If progress not found
   */
  async markWeekCompleted(enrollmentId, weekNumber, options = {}) {
    try {
      const progress = await this.findByEnrollmentAndWeek(
        enrollmentId,
        weekNumber,
        options
      );
      if (!progress) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_WEEK_PROGRESS.NOT_FOUND
        );
      }

      const updatedProgress = await progress.update(
        {
          status: ExperienceWeekProgressStatus.COMPLETED,
          completedAt: new Date(),
        },
        {
          transaction: options.transaction,
        }
      );

      return await this.helper.enrichProgress(updatedProgress);
    } catch (error) {
      console.error('Error in markWeekCompleted repository:', error);
      throw error;
    }
  }

  /**
   * Get completion statistics for an enrollment
   * @param {string} enrollmentId - Enrollment ID
   * @param {Object} options - Query options
   * @param {Object} options.transaction - Database transaction
   * @returns {Promise<Object>} Completion statistics
   */
  async getCompletionStats(enrollmentId, options = {}) {
    try {
      const progressRecords = await this.findByEnrollment(
        enrollmentId,
        options
      );
      return this.helper.calculateCompletionStats(progressRecords);
    } catch (error) {
      console.error('Error in getCompletionStats repository:', error);
      throw error;
    }
  }

  /**
   * Check if user can access a specific week
   * @param {string} enrollmentId - Enrollment ID
   * @param {number} weekNumber - Week number to check
   * @param {Object} options - Query options
   * @param {Object} options.transaction - Database transaction
   * @returns {Promise<boolean>} Whether user can access the week
   */
  async canAccessWeek(enrollmentId, weekNumber, options = {}) {
    try {
      if (weekNumber === 1) return true; // First week is always accessible

      // Check if previous week is completed
      const previousWeekProgress = await this.findByEnrollmentAndWeek(
        enrollmentId,
        weekNumber - 1,
        options
      );

      return (
        previousWeekProgress &&
        previousWeekProgress.status === ExperienceWeekProgressStatus.COMPLETED
      );
    } catch (error) {
      console.error('Error in canAccessWeek repository:', error);
      throw error;
    }
  }

  /**
   * Get next available week for user
   * @param {string} enrollmentId - Enrollment ID
   * @param {Object} options - Query options
   * @param {Object} options.transaction - Database transaction
   * @returns {Promise<number>} Next available week number
   */
  async getNextAvailableWeek(enrollmentId, options = {}) {
    try {
      const stats = await this.getCompletionStats(enrollmentId, options);
      return Math.min(stats.completedWeeks + 1, stats.totalWeeks);
    } catch (error) {
      console.error('Error in getNextAvailableWeek repository:', error);
      throw error;
    }
  }
}

module.exports = new ExperienceWeekProgressRepository();
