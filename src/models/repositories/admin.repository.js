/**
 * Admin Repository
 *
 * Handles data access operations for the Admin model
 */
const { ApiException } = require('@utils/exception.utils');
const Admin = require('@models/admin.model');
const { ADMIN, AUTH } = require('@utils/messages.utils');

class AdminRepository {
  /**
   * Find admin by ID
   * @param {string} id - Admin UUID
   * @returns {Promise<Admin>} Admin instance
   * @throws {ApiException} If admin not found
   */
  async findById(id) {
    try {
      const admin = await Admin.findByPk(id);

      if (!admin) {
        throw new ApiException(404, ADMIN.ADMIN_NOT_FOUND);
      }

      return admin;
    } catch (error) {
      // Log the error for debugging
      console.error('Error in findById repository:', error);

      // Simply rethrow the error
      throw error;
    }
  }

  /**
   * Find admin by email
   * @param {string} email - Admin email
   * @returns {Promise<Admin|null>} Admin instance or null
   */
  async findByEmail(email) {
    try {
      return await Admin.findOne({ where: { email: email.toLowerCase() } });
    } catch (error) {
      console.error('Error in findByEmail repository:', error);
      throw error;
    }
  }

  /**
   * Create a new admin
   * @param {Object} adminData - Admin data
   * @returns {Promise<Admin>} Created admin
   * @throws {ApiException} If email already exists
   */
  async create(adminData) {
    try {
      const existingAdmin = await this.findByEmail(adminData.email);

      if (existingAdmin) {
        throw new ApiException(409, AUTH.EMAIL_ALREADY_EXISTS);
      }

      return await Admin.create(adminData);
    } catch (error) {
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Update admin
   * @param {string} id - Admin UUID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Admin>} Updated admin
   * @throws {ApiException} If admin not found
   */
  async update(id, updateData) {
    try {
      const admin = await this.findById(id);
      await admin.update(updateData);
      return admin;
    } catch (error) {
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Delete admin
   * @param {string} id - Admin UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If admin not found
   */
  async delete(id) {
    try {
      const admin = await this.findById(id);
      await admin.destroy();
      return true;
    } catch (error) {
      console.error('Error in delete repository:', error);
      throw error;
    }
  }

  /**
   * Change admin password
   * @param {string} id - Admin UUID
   * @param {string} newPassword - New password
   * @returns {Promise<Admin>} Updated admin
   * @throws {ApiException} If admin not found
   */
  async changePassword(id, newPassword) {
    try {
      const admin = await Admin.findByPk(id);

      if (!admin) {
        throw new ApiException(404, ADMIN.ADMIN_NOT_FOUND);
      }

      admin.password = newPassword;
      await admin.save();
      return admin;
    } catch (error) {
      console.error('Error in changePassword repository:', error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new AdminRepository();
