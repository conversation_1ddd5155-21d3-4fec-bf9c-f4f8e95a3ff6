/**
 * Experience Discussion Repository
 *
 * Handles data access operations for the Experience Discussion model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { v4: uuidv4 } = require('uuid');
const { HttpStatus } = require('@utils/enums.utils');
const { DISCUSSION } = require('@utils/messages.utils');
const { Sequelize } = require('sequelize');
const { Op } = Sequelize;
const commonRepository = require('./common.repository');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      Experience: databaseService.getExperienceModel(),
      ExperienceDiscussion: databaseService.getExperienceDiscussionModel(),
      ExperienceDiscussionLike:
        databaseService.getExperienceDiscussionLikeModel(),
      User: databaseService.getUserModel(),
    };
  }

  _getDiscussionAttributes(userId) {
    return {
      exclude: ['experienceId', 'createdBy'],
      include: [
        [
          databaseService.getSequelize().literal(
            `EXISTS (
              SELECT 1 FROM "ExperienceDiscussionLike"
              WHERE "ExperienceDiscussionLike"."discussionId" = "ExperienceDiscussion"."id"
              AND "ExperienceDiscussionLike"."likedBy" = '${userId}'
            )`
          ),
          'isLiked',
        ],
        [
          databaseService.getSequelize().literal(
            `(
              SELECT COUNT(*)::INTEGER
              FROM "ExperienceDiscussionLike"
              WHERE "ExperienceDiscussionLike"."discussionId" = "ExperienceDiscussion"."id"
            )`
          ),
          'likeCount',
        ],
      ],
    };
  }

  _getDiscussionIncludes() {
    return [
      {
        model: this.models.User,
        as: 'creator',
        attributes: ['id', 'firstName', 'lastName', 'email', 'profilePic'],
      },
    ];
  }
}

/**
 * Helper class for Discussion operations
 */
class DiscussionHelper extends BaseRepository {
  async enrichDiscussionWithUserData(discussion, userId) {
    if (!userId) return discussion;

    const like = await this.models.ExperienceDiscussionLike.findOne({
      where: {
        discussionId: discussion.id,
        likedBy: userId,
      },
    });

    return {
      ...discussion.toJSON(),
      isLiked: !!like,
    };
  }

  async enrichDiscussionsWithUserData(discussions, userId) {
    if (!userId) return discussions;

    const discussionIds = discussions.map((discussion) => discussion.id);
    const likes = await this.models.ExperienceDiscussionLike.findAll({
      where: {
        discussionId: { [Op.in]: discussionIds },
        likedBy: userId,
      },
      raw: true,
    });

    return discussions.map((discussion) => ({
      ...discussion.toJSON(),
      isLiked: likes.some((like) => like.discussionId === discussion.id),
    }));
  }
}

/**
 * Experience Discussion Repository class
 */
class ExperienceDiscussionRepository extends BaseRepository {
  constructor() {
    super();
    this.helper = new DiscussionHelper();
  }

  /**
   * Create a new discussion
   * @param {Object} data - Discussion data
   * @param {Object} transaction - Database transaction
   * @returns {Promise<Object>} Created discussion
   */
  async createDiscussion(data, transaction) {
    const discussionData = {
      id: uuidv4(),
      ...data,
    };

    const discussion = await this.models.ExperienceDiscussion.create(
      discussionData,
      { transaction }
    );

    // Return the discussion with proper attributes and includes
    const createdDiscussion = await this.models.ExperienceDiscussion.findByPk(
      discussion.id,
      {
        include: this._getDiscussionIncludes(),
        attributes: this._getDiscussionAttributes(data.createdBy),
      }
    );

    return createdDiscussion;
  }

  /**
   * Get discussions by experience ID with pagination
   * @param {string} experienceId - Experience ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @param {string} userId - User ID for like status
   * @returns {Promise<Object>} Discussions with pagination
   */
  async findDiscussionsByExperienceId(experienceId, page, limit, userId) {
    const offset = commonRepository.calculateOffset(page, limit);

    const discussions = await this.models.ExperienceDiscussion.findAndCountAll({
      where: { experienceId },
      include: this._getDiscussionIncludes(),
      attributes: this._getDiscussionAttributes(userId),
      order: [['createdAt', 'DESC']],
      limit,
      offset,
    });

    const enrichedDiscussions = await this.helper.enrichDiscussionsWithUserData(
      discussions.rows,
      userId
    );

    return {
      discussions: enrichedDiscussions,
      pagination: commonRepository.buildPaginationInfo(
        discussions.count,
        page,
        limit
      ),
    };
  }

  /**
   * Toggle like status for a discussion
   * @param {string} discussionId - Discussion ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated discussion with like status
   */
  async toggleDiscussionLike(discussionId, userId) {
    const discussion = await this.models.ExperienceDiscussion.findByPk(
      discussionId,
      {
        include: this._getDiscussionIncludes(),
      }
    );

    if (!discussion) {
      throw new ApiException(HttpStatus.NOT_FOUND, DISCUSSION.NOT_FOUND);
    }

    const existingLike = await this.models.ExperienceDiscussionLike.findOne({
      where: {
        discussionId,
        likedBy: userId,
      },
    });

    if (existingLike) {
      await existingLike.destroy();
      return { isLiked: false };
    } else {
      await this.models.ExperienceDiscussionLike.create({
        discussionId,
        likedBy: userId,
      });
      return { isLiked: true };
    }
  }
}

module.exports = new ExperienceDiscussionRepository();
