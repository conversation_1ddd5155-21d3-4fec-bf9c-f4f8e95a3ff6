/**
 * Experience Enrollment Repository
 *
 * Handles data access operations for the ExperienceEnrollment model
 */
const databaseService = require('@config/database.config');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { EXPERIENCE_ENROLLMENT } = require('@utils/messages.utils');
const {
  ExperienceEnrollmentStatus,
  ExperienceWeekProgressStatus,
} = require('@utils/enums.utils');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      ExperienceEnrollment: databaseService.getExperienceEnrollmentModel(),
      Experience: databaseService.getExperienceModel(),
      User: databaseService.getUserModel(),
      ExperienceWeekProgress: databaseService.getExperienceWeekProgressModel(),
      ExperienceWeek: databaseService.getExperienceWeekModel(),
    };
  }

  _getEnrollmentAttributes() {
    return {
      exclude: ['experienceId', 'userId'],
    };
  }

  _getEnrollmentIncludes() {
    return [
      {
        model: this.models.Experience,
        as: 'experience',
        attributes: ['id', 'title', 'shortDescription', 'experienceLength'],
      },
      {
        model: this.models.User,
        as: 'user',
        attributes: ['id', 'firstName', 'lastName', 'profilePic'],
      },
    ];
  }
}

/**
 * Helper class for Experience Enrollment operations
 */
class ExperienceEnrollmentHelper extends BaseRepository {
  /**
   * Find enrollment by experience and user
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Enrollment object
   */
  async findByExperienceAndUser(experienceId, userId) {
    try {
      const enrollment = await this.models.ExperienceEnrollment.findOne({
        where: { experienceId, userId },
        attributes: this._getEnrollmentAttributes(),
        include: this._getEnrollmentIncludes(),
      });

      return enrollment || null;
    } catch (error) {
      console.error('Error in findByExperienceAndUser repository:', error);
      throw error;
    }
  }

  /**
   * Initialize week progress records for an enrollment
   * @param {string} enrollmentId - Enrollment ID
   * @param {string} experienceId - Experience ID
   * @returns {Promise<void>}
   * @throws {ApiException} If experience has no weeks
   */
  async initializeWeekProgress(enrollmentId, experienceId) {
    // Get all weeks for the experience
    const experience = await this.models.Experience.findByPk(experienceId, {
      include: [
        {
          model: this.models.ExperienceWeek,
          as: 'weeks',
          attributes: ['id', 'weekNumber'],
        },
      ],
    });

    if (!experience || !experience.weeks || experience.weeks.length === 0) {
      throw new ApiException(
        HttpStatus.BAD_REQUEST,
        'Experience has no weeks defined'
      );
    }

    // Create progress records for all weeks
    const progressData = experience.weeks.map((week) => ({
      enrollmentId,
      experienceWeekId: week.id,
      weekNumber: week.weekNumber,
      status: ExperienceWeekProgressStatus.PENDING,
    }));

    await this.models.ExperienceWeekProgress.bulkCreate(progressData);
  }

  /**
   * Validate if user can complete the week
   * @param {string} enrollmentId - Enrollment ID
   * @param {number} weekNumber - Week number
   * @returns {Promise<void>}
   * @throws {ApiException} If validation fails
   */
  async validateWeekCompletion(enrollmentId, weekNumber) {
    if (weekNumber === 1) return;

    const canAccess = await this.canAccessWeek(enrollmentId, weekNumber);

    if (!canAccess) {
      throw new ApiException(
        HttpStatus.BAD_REQUEST,
        'You must complete previous weeks before accessing this week'
      );
    }
  }

  /**
   * Check if user can access a specific week
   * @param {string} enrollmentId - Enrollment ID
   * @param {number} weekNumber - Week number to check
   * @returns {Promise<boolean>} Whether the week can be accessed
   */
  async canAccessWeek(enrollmentId, weekNumber) {
    if (weekNumber === 1) return true;

    const previousWeek = await this.models.ExperienceWeekProgress.findOne({
      where: {
        enrollmentId,
        weekNumber: weekNumber - 1,
      },
    });

    return (
      previousWeek &&
      previousWeek.status === ExperienceWeekProgressStatus.COMPLETED
    );
  }

  /**
   * Mark a week as completed
   * @param {string} enrollmentId - Enrollment ID
   * @param {number} weekNumber - Week number to complete
   * @returns {Promise<void>}
   */
  async markWeekCompleted(enrollmentId, weekNumber) {
    await this.models.ExperienceWeekProgress.update(
      {
        status: ExperienceWeekProgressStatus.COMPLETED,
        completedAt: new Date(),
      },
      {
        where: {
          enrollmentId,
          weekNumber,
        },
      }
    );
  }

  /**
   * Get completion statistics for an enrollment
   * @param {string} enrollmentId - Enrollment ID
   * @returns {Promise<Object>} Completion statistics
   */
  async getCompletionStats(enrollmentId) {
    const totalWeeks = await this.models.ExperienceWeekProgress.count({
      where: { enrollmentId },
    });

    const completedWeeks = await this.models.ExperienceWeekProgress.count({
      where: {
        enrollmentId,
        status: ExperienceWeekProgressStatus.COMPLETED,
      },
    });

    const currentWeek = await this.models.ExperienceWeekProgress.findOne({
      where: {
        enrollmentId,
        status: ExperienceWeekProgressStatus.PENDING,
      },
      order: [['weekNumber', 'ASC']],
    });

    const progressPercentage = Math.round((completedWeeks / totalWeeks) * 100);
    const isCompleted = completedWeeks === totalWeeks;

    return {
      totalWeeks,
      completedWeeks,
      currentWeek: isCompleted
        ? totalWeeks
        : currentWeek
          ? currentWeek.weekNumber
          : completedWeeks + 1,
      progressPercentage,
      isCompleted,
    };
  }

  /**
   * Update enrollment progress after completing a week
   * @param {string} enrollmentId - Enrollment ID
   * @returns {Promise<Object>} Updated enrollment with progress
   */
  async updateEnrollmentProgress(enrollmentId) {
    const stats = await this.getCompletionStats(enrollmentId);

    const updateData = {
      completedWeeks: stats.completedWeeks,
      currentWeek: stats.currentWeek,
    };

    if (stats.isCompleted) {
      updateData.status = ExperienceEnrollmentStatus.COMPLETED;
    }

    const updatedEnrollment = await this.models.ExperienceEnrollment.update(
      updateData,
      {
        where: { id: enrollmentId },
        returning: true,
      }
    );

    return {
      enrollment: updatedEnrollment[1][0],
      progress: stats,
    };
  }
}

/**
 * Main Experience Enrollment Repository class
 */
class ExperienceEnrollmentRepository extends BaseRepository {
  constructor() {
    super();
    this.helper = new ExperienceEnrollmentHelper();
  }

  /**
   * Find enrollment by experience and user
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Enrollment object
   */
  async findByExperienceAndUser(experienceId, userId) {
    return this.helper.findByExperienceAndUser(experienceId, userId);
  }

  /**
   * Enroll a user in an experience
   * @param {Object} data - Enrollment data
   * @returns {Promise<Object>} Created enrollment
   */
  async enrollUser(data) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      // Check if experience exists and get creator ID
      const experience = await this.models.Experience.findByPk(
        data.experienceId,
        {
          attributes: ['createdBy'],
        }
      );

      if (!experience) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_ENROLLMENT.EXPERIENCE_NOT_FOUND
        );
      }

      // Check if user is the creator of the experience
      if (experience.createdBy === data.userId) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE_ENROLLMENT.CANNOT_ENROLL_OWN_EXPERIENCE
        );
      }

      // Check if user is already enrolled
      const existingEnrollment = await this.findByExperienceAndUser(
        data.experienceId,
        data.userId
      );

      if (existingEnrollment) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE_ENROLLMENT.ALREADY_ENROLLED
        );
      }

      // Create enrollment with default status
      const enrollment = await this.models.ExperienceEnrollment.create({
        ...data,
        status: ExperienceEnrollmentStatus.REGISTERED,
      });

      // Initialize week progress
      await this.helper.initializeWeekProgress(
        enrollment.id,
        data.experienceId
      );

      await transaction.commit();
      return enrollment;
    } catch (error) {
      await transaction.rollback();
      console.error('Error in enrollUser repository:', error);
      throw error;
    }
  }

  /**
   * Complete a week for an enrollment
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {number} weekNumber - Week number to complete
   * @returns {Promise<Object>} Updated enrollment with progress
   * @throws {ApiException} If validation fails
   */
  async completeWeek(experienceId, userId, weekNumber) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      // Get enrollment
      const enrollment = await this.findByExperienceAndUser(
        experienceId,
        userId
      );

      if (!enrollment) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_ENROLLMENT.NOT_FOUND
        );
      }

      // Validate week completion
      await this.helper.validateWeekCompletion(enrollment.id, weekNumber);

      // Mark week as completed
      await this.helper.markWeekCompleted(enrollment.id, weekNumber);

      // Update enrollment progress
      const result = await this.helper.updateEnrollmentProgress(enrollment.id);

      await transaction.commit();
      return result;
    } catch (error) {
      await transaction.rollback();
      console.error('Error in completeWeek repository:', error);
      throw error;
    }
  }

  /**
   * Get detailed progress for an enrollment
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Detailed progress information
   * @throws {ApiException} If enrollment not found
   */
  async getEnrollmentProgress(experienceId, userId) {
    try {
      const enrollment = await this.findByExperienceAndUser(
        experienceId,
        userId
      );

      if (!enrollment) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_ENROLLMENT.NOT_FOUND
        );
      }

      const [weekProgress, stats] = await Promise.all([
        this.models.ExperienceWeekProgress.findAll({
          where: { enrollmentId: enrollment.id },
          include: [
            {
              model: this.models.ExperienceWeek,
              as: 'experienceWeek',
              attributes: ['id', 'weekNumber', 'title'],
            },
          ],
          order: [['weekNumber', 'ASC']],
        }),
        this.helper.getCompletionStats(enrollment.id),
      ]);

      return {
        weekProgress,
        stats,
      };
    } catch (error) {
      console.error('Error in getEnrollmentProgress repository:', error);
      throw error;
    }
  }
}

module.exports = new ExperienceEnrollmentRepository();
