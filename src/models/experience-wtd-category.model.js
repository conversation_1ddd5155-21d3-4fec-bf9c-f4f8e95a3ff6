/**
 * ExperienceWtdCategory Model
 * Junction table for Experience and WtdCategory many-to-many relationship
 */
const { Model, DataTypes } = require('sequelize');

class ExperienceWtdCategory extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        experienceId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Experience',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        wtdCategoryId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'WtdCategory',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'ExperienceWtdCategory',
        tableName: 'ExperienceWtdCategory',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['experienceId', 'wtdCategoryId'],
            name: 'experience_wtd_categories_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
    // Associations are defined in the main models
  }
}

module.exports = ExperienceWtdCategory;
