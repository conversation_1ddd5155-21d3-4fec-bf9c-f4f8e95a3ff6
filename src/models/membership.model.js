/**
 * Membership Model
 */
const { Model, DataTypes } = require('sequelize');
const { MembershipType } = require('@utils/enums.utils');

class Membership extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
        },
        membershipType: {
          type: DataTypes.ENUM(...MembershipType.values),
          allowNull: false,
        },
        startDate: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW,
        },
        endDate: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: true,
        },
        paymentReference: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        amount: {
          type: DataTypes.DECIMAL(10, 2),
          allowNull: true,
        },
        currency: {
          type: DataTypes.STRING(3),
          allowNull: true,
          defaultValue: 'USD',
        },
      },
      {
        sequelize,
        modelName: 'Membership',
        tableName: 'Membership',
        timestamps: true,
      }
    );
  }

  // Define associations
  static associate(models) {
    Membership.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE', // Delete membership when user is deleted
    });
  }
}

module.exports = Membership;
