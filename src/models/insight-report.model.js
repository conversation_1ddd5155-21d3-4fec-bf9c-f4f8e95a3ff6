/**
 * InsightReport Model
 * Represents reports made against insights
 */
const { Model, DataTypes } = require('sequelize');

class InsightReport extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        insightId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Insight',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        reportedBy: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        reason: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'InsightReport',
        tableName: 'InsightReport',
        timestamps: true,
        indexes: [
          {
            fields: ['insightId'],
          },
          {
            fields: ['reportedBy'],
          },
          {
            fields: ['createdAt'],
          },
          {
            unique: true,
            fields: ['insightId', 'reportedBy'],
            name: 'unique_insight_report_per_user',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to Insight
    this.belongsTo(models.Insight, {
      foreignKey: 'insightId',
      as: 'insight',
      onDelete: 'CASCADE',
    });

    // Belongs to User (reporter)
    this.belongsTo(models.User, {
      foreignKey: 'reportedBy',
      as: 'reporter',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = InsightReport;
