/**
 * ExperienceMedia Model
 * Represents media files associated with experiences
 */
const { Model, DataTypes } = require('sequelize');
const { MediaType } = require('@utils/enums.utils');

class ExperienceMedia extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        experienceId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Experience',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        type: {
          type: DataTypes.ENUM(...MediaType.values),
          allowNull: false,
        },
        url: {
          type: DataTypes.TEXT,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        title: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        order: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          validate: {
            min: 0,
          },
        },
      },
      {
        sequelize,
        modelName: 'ExperienceMedia',
        tableName: 'ExperienceMedia',
        timestamps: true,
        indexes: [
          {
            fields: ['experienceId'],
            name: 'experience_media_experience_id_idx',
          },
          {
            fields: ['type'],
            name: 'experience_media_type_idx',
          },
          {
            fields: ['experienceId', 'order'],
            name: 'experience_media_order_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to Experience
    this.belongsTo(models.Experience, {
      foreignKey: 'experienceId',
      as: 'experience',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = ExperienceMedia;
