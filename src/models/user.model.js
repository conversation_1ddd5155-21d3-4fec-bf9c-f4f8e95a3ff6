/**
 * User Model
 */
const { Model, DataTypes } = require('sequelize');
const bcrypt = require('bcrypt');
const { UserType } = require('@utils/enums.utils');

class User extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        firstName: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        lastName: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        email: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true,
          validate: {
            isEmail: true,
          },
          set(value) {
            this.setDataValue('email', value.toLowerCase());
          },
        },
        password: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        userType: {
          type: DataTypes.ENUM(...UserType.values),
          allowNull: false,
          defaultValue: UserType.EDUCATOR,
        },
        position: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        state: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        country: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        companyName: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        profilePic: {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: null,
        },
        website: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        isPrivate: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        isDepartmentPrivate: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
      },
      {
        sequelize,
        modelName: 'User',
        tableName: 'User',
        timestamps: true,
        hooks: {
          beforeSave: async (user) => {
            if (user.changed('password')) {
              user.password = await bcrypt.hash(user.password, 10);
            }
          },
        },
      }
    );
  }

  // Define associations
  static associate(models) {
    User.hasMany(models.Membership, {
      foreignKey: 'userId',
      as: 'memberships',
      onDelete: 'CASCADE', // Delete memberships when user is deleted
    });

    // Bookmarked insights association
    User.belongsToMany(models.Insight, {
      through: models.BookmarkedInsight,
      as: 'bookmarkedInsights',
      foreignKey: 'userId',
      otherKey: 'insightId',
      onDelete: 'CASCADE',
    });

    // Liked insights association
    User.belongsToMany(models.Insight, {
      through: models.LikedInsight,
      as: 'likedInsights',
      foreignKey: 'userId',
      otherKey: 'insightId',
      onDelete: 'CASCADE',
    });

    // Implemented insights association
    User.belongsToMany(models.Insight, {
      through: models.ImplementedInsight,
      as: 'implementedInsights',
      foreignKey: 'userId',
      otherKey: 'insightId',
      onDelete: 'CASCADE',
    });

    // Has many contributions
    User.hasMany(models.Contribution, {
      foreignKey: 'contributedBy',
      as: 'contributions',
      onDelete: 'CASCADE',
    });

    // Has many contribution likes
    User.hasMany(models.ContributionLike, {
      foreignKey: 'likedBy',
      as: 'contributionLikes',
      onDelete: 'CASCADE',
    });

    // IceBreaker association
    User.hasOne(models.WtdIceBreaker, {
      foreignKey: 'userId',
      as: 'iceBreaker',
      onDelete: 'CASCADE',
    });

    // Focus association
    User.belongsToMany(models.Focus, {
      through: models.UserFocus,
      foreignKey: 'userId',
      otherKey: 'focusId',
      as: 'focuses',
      onDelete: 'CASCADE',
    });

    // Followers association
    User.belongsToMany(models.User, {
      through: models.Follow,
      as: 'followers',
      foreignKey: 'followingId',
      otherKey: 'followerId',
      onDelete: 'CASCADE',
    });

    // Following association
    User.belongsToMany(models.User, {
      through: models.Follow,
      as: 'following',
      foreignKey: 'followerId',
      otherKey: 'followingId',
      onDelete: 'CASCADE',
    });
  }

  // Instance Methods
  async verifyPassword(password) {
    return bcrypt.compare(password, this.password);
  }

  toJSON() {
    const values = { ...this.get() };
    delete values.password;
    return values;
  }
}

module.exports = User;
