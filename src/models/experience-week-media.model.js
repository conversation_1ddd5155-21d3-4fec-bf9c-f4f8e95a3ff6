/**
 * ExperienceWeekMedia Model
 * Represents media files associated with experience weeks
 */
const { Model, DataTypes } = require('sequelize');
const { MediaType } = require('@utils/enums.utils');

class ExperienceWeekMedia extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        experienceWeekId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'ExperienceWeek',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        type: {
          type: DataTypes.ENUM(...MediaType.values),
          allowNull: false,
        },
        url: {
          type: DataTypes.TEXT,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        title: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        order: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          validate: {
            min: 0,
          },
        },
      },
      {
        sequelize,
        modelName: 'ExperienceWeekMedia',
        tableName: 'ExperienceWeekMedia',
        timestamps: true,
        indexes: [
          {
            fields: ['experienceWeekId'],
            name: 'experience_week_media_week_id_idx',
          },
          {
            fields: ['type'],
            name: 'experience_week_media_type_idx',
          },
          {
            fields: ['experienceWeekId', 'order'],
            name: 'experience_week_media_order_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to ExperienceWeek
    this.belongsTo(models.ExperienceWeek, {
      foreignKey: 'experienceWeekId',
      as: 'experienceWeek',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = ExperienceWeekMedia;
