'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // First, backup existing data
      const existingData = await queryInterface.sequelize.query(
        'SELECT * FROM "WtdIceBreaker"',
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      console.log(
        `Found ${existingData.length} ice breaker records to migrate`
      );

      // Handle duplicates - keep only the most recent record for each user
      const uniqueData = {};
      existingData.forEach((record) => {
        if (
          !uniqueData[record.userId] ||
          new Date(record.updatedAt) >
            new Date(uniqueData[record.userId].updatedAt)
        ) {
          uniqueData[record.userId] = record;
        }
      });

      const finalData = Object.values(uniqueData);
      console.log(`After deduplication: ${finalData.length} unique records`);

      // Drop the existing table
      await queryInterface.dropTable('WtdIceBreaker', { transaction });

      // Recreate the table with UUID primary key and unique userId constraint
      await queryInterface.createTable(
        'WtdIceBreaker',
        {
          id: {
            type: Sequelize.UUID,
            defaultValue: Sequelize.UUIDV4,
            primaryKey: true,
            allowNull: false,
          },
          userId: {
            type: Sequelize.UUID,
            allowNull: false,
            unique: true,
            references: {
              model: 'User',
              key: 'id',
            },
            onDelete: 'CASCADE',
          },
          teachSubject: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          contentIn3Words: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          inspiration: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          funFact: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          magicButton: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          resourcesEmojis: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          themeSong: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          impactTeacher: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          isPrivate: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
          },
        },
        { transaction }
      );

      // Restore the data with new UUID primary keys
      if (finalData.length > 0) {
        for (const record of finalData) {
          await queryInterface.sequelize.query(
            `INSERT INTO "WtdIceBreaker" (
              "id", "userId", "teachSubject", "contentIn3Words", "inspiration",
              "funFact", "magicButton", "resourcesEmojis", "themeSong",
              "impactTeacher", "isPrivate", "createdAt", "updatedAt"
            ) VALUES (
              gen_random_uuid(), :userId, :teachSubject, :contentIn3Words, :inspiration,
              :funFact, :magicButton, :resourcesEmojis, :themeSong,
              :impactTeacher, :isPrivate, :createdAt, :updatedAt
            )`,
            {
              replacements: {
                userId: record.userId,
                teachSubject: record.teachSubject,
                contentIn3Words: record.contentIn3Words,
                inspiration: record.inspiration,
                funFact: record.funFact,
                magicButton: record.magicButton,
                resourcesEmojis: record.resourcesEmojis,
                themeSong: record.themeSong,
                impactTeacher: record.impactTeacher,
                isPrivate: record.isPrivate || false,
                createdAt: record.createdAt || new Date(),
                updatedAt: record.updatedAt || new Date(),
              },
              transaction,
            }
          );
        }
      }

      await transaction.commit();
      console.log(
        'Successfully migrated WtdIceBreaker table to use UUID primary key and unique userId constraint'
      );
    } catch (error) {
      await transaction.rollback();
      console.error('Error migrating WtdIceBreaker table:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Backup existing data
      const existingData = await queryInterface.sequelize.query(
        'SELECT * FROM "WtdIceBreaker"',
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      // Drop the current table
      await queryInterface.dropTable('WtdIceBreaker', { transaction });

      // Recreate with original INTEGER primary key structure
      await queryInterface.createTable(
        'WtdIceBreaker',
        {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER,
          },
          userId: {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
              model: 'User',
              key: 'id',
            },
            onDelete: 'CASCADE',
          },
          teachSubject: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          contentIn3Words: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          inspiration: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          funFact: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          magicButton: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          resourcesEmojis: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          themeSong: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          impactTeacher: {
            type: Sequelize.TEXT,
            allowNull: true,
          },
          isPrivate: {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
          },
        },
        { transaction }
      );

      // Add back the index on userId (non-unique)
      await queryInterface.addIndex('WtdIceBreaker', ['userId'], {
        transaction,
      });

      // Restore data
      if (existingData.length > 0) {
        const dataToInsert = existingData.map((record) => ({
          userId: record.userId,
          teachSubject: record.teachSubject,
          contentIn3Words: record.contentIn3Words,
          inspiration: record.inspiration,
          funFact: record.funFact,
          magicButton: record.magicButton,
          resourcesEmojis: record.resourcesEmojis,
          themeSong: record.themeSong,
          impactTeacher: record.impactTeacher,
          isPrivate: record.isPrivate || false,
          createdAt: record.createdAt || new Date(),
          updatedAt: record.updatedAt || new Date(),
        }));

        await queryInterface.bulkInsert('WtdIceBreaker', dataToInsert, {
          transaction,
        });
      }

      await transaction.commit();
      console.log(
        'Successfully reverted WtdIceBreaker table to use INTEGER primary key'
      );
    } catch (error) {
      await transaction.rollback();
      console.error('Error reverting WtdIceBreaker table:', error);
      throw error;
    }
  },
};
