/**
 * Migration: Fix duplicate Insight constraints and Membership onUpdate CASCADE
 * 1. Remove duplicate Insight_createdBy_fkey1 constraint
 * 2. Fix Membership table onUpdate CASCADE
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('🔧 Fixing duplicate constraints and membership table...');

    try {
      // Fix 1: Remove duplicate Insight constraint (Insight_createdBy_fkey1)
      console.log('🔍 Checking for duplicate Insight constraints...');

      const [insightConstraints] = await queryInterface.sequelize.query(`
        SELECT tc.constraint_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu
          ON tc.constraint_name = kcu.constraint_name
        WHERE tc.table_name = 'Insight'
          AND kcu.column_name = 'createdBy'
          AND tc.constraint_type = 'FOREIGN KEY'
        ORDER BY tc.constraint_name;
      `);

      if (insightConstraints.length > 1) {
        // Keep the first one, remove the duplicate(s)
        for (let i = 1; i < insightConstraints.length; i++) {
          const duplicateConstraint = insightConstraints[i].constraint_name;
          console.log(
            `🗑️  Removing duplicate constraint: ${duplicateConstraint}`
          );

          await queryInterface.sequelize.query(`
            ALTER TABLE "Insight" DROP CONSTRAINT "${duplicateConstraint}"
          `);
        }
        console.log(
          `✅ Removed ${insightConstraints.length - 1} duplicate Insight constraint(s)`
        );
      } else {
        console.log('ℹ️  No duplicate Insight constraints found');
      }

      // Fix 2: Update Membership table onUpdate CASCADE
      console.log('🔍 Checking Membership table constraints...');

      const [membershipConstraints] = await queryInterface.sequelize.query(`
        SELECT tc.constraint_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu
          ON tc.constraint_name = kcu.constraint_name
        WHERE tc.table_name = 'Membership'
          AND kcu.column_name = 'userId'
          AND tc.constraint_type = 'FOREIGN KEY'
      `);

      if (membershipConstraints.length > 0) {
        const constraintName = membershipConstraints[0].constraint_name;

        // Check if it has onUpdate CASCADE
        const [constraintDetails] = await queryInterface.sequelize.query(`
          SELECT update_rule
          FROM information_schema.referential_constraints
          WHERE constraint_name = '${constraintName}'
        `);

        if (
          constraintDetails.length > 0 &&
          constraintDetails[0].update_rule === 'CASCADE'
        ) {
          console.log(`🔧 Fixing Membership constraint: ${constraintName}`);

          // Drop the existing constraint
          await queryInterface.sequelize.query(`
            ALTER TABLE "Membership" DROP CONSTRAINT "${constraintName}"
          `);

          // Recreate with proper constraints (no onUpdate CASCADE, but with onDelete CASCADE)
          await queryInterface.sequelize.query(`
            ALTER TABLE "Membership"
            ADD CONSTRAINT "${constraintName}"
            FOREIGN KEY ("userId")
            REFERENCES "User"(id)
            ON DELETE CASCADE
          `);

          console.log(
            '✅ Updated Membership.userId constraint (removed onUpdate CASCADE)'
          );
        } else {
          console.log(
            'ℹ️  Membership constraint already clean (no onUpdate CASCADE)'
          );
        }
      } else {
        console.log('⚠️  No Membership userId constraint found');
      }

      console.log('\n🎉 Constraint fixes completed successfully!');
    } catch (error) {
      console.error('❌ Error during constraint fixes:', error.message);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    console.log('⚠️  Rollback not implemented for constraint fixes');
    console.log(
      '   These changes improve database consistency and are safe to keep'
    );
    console.log(
      '   Manual rollback would require recreating the duplicate constraint'
    );
  },
};
