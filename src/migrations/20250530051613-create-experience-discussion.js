'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ExperienceDiscussion', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      experienceId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Experience',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdBy: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add indexes
    await queryInterface.addIndex('ExperienceDiscussion', ['experienceId'], {
      name: 'experience_discussion_experience_id_idx',
    });

    await queryInterface.addIndex('ExperienceDiscussion', ['createdBy'], {
      name: 'experience_discussion_created_by_idx',
    });

    await queryInterface.addIndex('ExperienceDiscussion', ['createdAt'], {
      name: 'experience_discussion_created_at_idx',
    });

    await queryInterface.addIndex(
      'ExperienceDiscussion',
      ['experienceId', 'createdAt'],
      {
        name: 'experience_discussion_experience_created_idx',
      }
    );
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex(
      'ExperienceDiscussion',
      'experience_discussion_experience_id_idx'
    );
    await queryInterface.removeIndex(
      'ExperienceDiscussion',
      'experience_discussion_created_by_idx'
    );
    await queryInterface.removeIndex(
      'ExperienceDiscussion',
      'experience_discussion_created_at_idx'
    );
    await queryInterface.removeIndex(
      'ExperienceDiscussion',
      'experience_discussion_experience_created_idx'
    );

    // Then remove the table
    await queryInterface.dropTable('ExperienceDiscussion');
  },
};
