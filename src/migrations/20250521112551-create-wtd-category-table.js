'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('WtdCategory', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    // Create a unique index on the lowercase version of the name column
    // This ensures case-insensitive uniqueness while preserving the original case in the column
    await queryInterface.sequelize.query(`
      CREATE UNIQUE INDEX wtd_category_name_lower_idx ON "WtdCategory" (LOWER(name));
    `);
  },

  async down(queryInterface, Sequelize) {
    // Drop the index first
    await queryInterface.sequelize.query(`
      DROP INDEX IF EXISTS wtd_category_name_lower_idx;
    `);

    // Drop the table
    await queryInterface.dropTable('WtdCategory');
  },
};
