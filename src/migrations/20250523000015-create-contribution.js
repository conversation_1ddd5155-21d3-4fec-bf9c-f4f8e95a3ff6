/**
 * Migration: Create Contribution table
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Contribution', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      insightId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Insight',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      contributedBy: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add indexes
    await queryInterface.addIndex('Contribution', ['insightId'], {
      name: 'contribution_insight_id_idx',
    });

    await queryInterface.addIndex('Contribution', ['contributedBy'], {
      name: 'contribution_contributed_by_idx',
    });

    await queryInterface.addIndex('Contribution', ['createdAt'], {
      name: 'contribution_created_at_idx',
    });

    // Composite index for insight contributions ordered by creation time
    await queryInterface.addIndex('Contribution', ['insightId', 'createdAt'], {
      name: 'contribution_insight_created_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Contribution');
  },
};
