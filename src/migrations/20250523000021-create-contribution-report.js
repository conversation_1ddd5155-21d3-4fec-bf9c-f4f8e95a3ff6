'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ContributionReport', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      contributionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Contribution',
          key: 'id',
        },
        onUpdate: 'NO ACTION',
        onDelete: 'CASCADE',
      },
      reportedBy: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onUpdate: 'NO ACTION',
        onDelete: 'CASCADE',
      },
      reason: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Create indexes
    await queryInterface.addIndex('ContributionReport', ['contributionId']);
    await queryInterface.addIndex('ContributionReport', ['reportedBy']);
    await queryInterface.addIndex('ContributionReport', ['createdAt']);

    // Create unique constraint to prevent duplicate reports from same user for same contribution
    await queryInterface.addConstraint('ContributionReport', {
      fields: ['contributionId', 'reportedBy'],
      type: 'unique',
      name: 'unique_contribution_report_per_user',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ContributionReport');
  },
};
