'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('InsightReport', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      insightId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Insight',
          key: 'id',
        },
        onUpdate: 'NO ACTION',
        onDelete: 'CASCADE',
      },
      reportedBy: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onUpdate: 'NO ACTION',
        onDelete: 'CASCADE',
      },
      reason: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Create indexes
    await queryInterface.addIndex('InsightReport', ['insightId']);
    await queryInterface.addIndex('InsightReport', ['reportedBy']);
    await queryInterface.addIndex('InsightReport', ['createdAt']);

    // Create unique constraint to prevent duplicate reports from same user for same insight
    await queryInterface.addConstraint('InsightReport', {
      fields: ['insightId', 'reportedBy'],
      type: 'unique',
      name: 'unique_insight_report_per_user',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('InsightReport');
  },
};
