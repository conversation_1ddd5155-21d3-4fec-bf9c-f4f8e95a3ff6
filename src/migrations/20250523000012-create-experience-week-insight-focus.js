/**
 * Migration: Create ExperienceWeekInsightFocus junction table
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ExperienceWeekInsightFocus', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      experienceWeekInsightId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceWeekInsight',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      focusId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Focus',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add unique constraint for experienceWeekInsightId + focusId
    await queryInterface.addIndex(
      'ExperienceWeekInsightFocus',
      ['experienceWeekInsightId', 'focusId'],
      {
        unique: true,
        name: 'experience_week_insight_focus_unique_idx',
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ExperienceWeekInsightFocus');
  },
};
