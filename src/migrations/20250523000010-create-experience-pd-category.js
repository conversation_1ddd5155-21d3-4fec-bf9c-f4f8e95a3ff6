/**
 * Migration: Create ExperiencePdCategory junction table
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ExperiencePdCategory', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      experienceId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Experience',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      pdCategoryId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'PdCategory',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add unique constraint for experienceId + pdCategoryId
    await queryInterface.addIndex(
      'ExperiencePdCategory',
      ['experienceId', 'pdCategoryId'],
      {
        unique: true,
        name: 'experience_pd_category_unique_idx',
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ExperiencePdCategory');
  },
};
