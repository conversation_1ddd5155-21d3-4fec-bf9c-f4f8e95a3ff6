/**
 * Migration: Create ExperienceEnrollment table
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ExperienceEnrollment', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      experienceId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Experience',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      startDate: {
        type: Sequelize.DATEONLY,
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM('REGISTERED', 'COMPLETED'),
        defaultValue: 'REGISTERED',
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add indexes
    await queryInterface.addIndex('ExperienceEnrollment', ['experienceId'], {
      name: 'experience_enrollment_experience_id_idx',
    });

    await queryInterface.addIndex('ExperienceEnrollment', ['userId'], {
      name: 'experience_enrollment_user_id_idx',
    });

    await queryInterface.addIndex('ExperienceEnrollment', ['status'], {
      name: 'experience_enrollment_status_idx',
    });

    // Add unique constraint for experienceId + userId
    await queryInterface.addIndex(
      'ExperienceEnrollment',
      ['experienceId', 'userId'],
      {
        unique: true,
        name: 'experience_enrollment_unique_idx',
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ExperienceEnrollment');
  },
};
