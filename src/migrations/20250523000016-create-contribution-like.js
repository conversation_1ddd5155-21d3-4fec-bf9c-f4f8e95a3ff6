/**
 * Migration: Create ContributionLike table
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ContributionLike', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      contributionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Contribution',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      likedBy: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add unique constraint for contributionId + likedBy (one like per user per contribution)
    await queryInterface.addIndex(
      'ContributionLike',
      ['contributionId', 'likedBy'],
      {
        unique: true,
        name: 'contribution_like_unique_idx',
      }
    );

    // Add indexes for performance
    await queryInterface.addIndex('ContributionLike', ['contributionId'], {
      name: 'contribution_like_contribution_id_idx',
    });

    await queryInterface.addIndex('ContributionLike', ['likedBy'], {
      name: 'contribution_like_liked_by_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ContributionLike');
  },
};
