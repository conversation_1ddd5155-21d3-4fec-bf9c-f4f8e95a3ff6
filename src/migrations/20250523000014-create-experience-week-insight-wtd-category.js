/**
 * Migration: Create ExperienceWeekInsightWtdCategory junction table
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ExperienceWeekInsightWtdCategory', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      experienceWeekInsightId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceWeekInsight',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      wtdCategoryId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'WtdCategory',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add unique constraint for experienceWeekInsightId + wtdCategoryId
    await queryInterface.addIndex(
      'ExperienceWeekInsightWtdCategory',
      ['experienceWeekInsightId', 'wtdCategoryId'],
      {
        unique: true,
        name: 'experience_week_insight_wtd_category_unique_idx',
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ExperienceWeekInsightWtdCategory');
  },
};
