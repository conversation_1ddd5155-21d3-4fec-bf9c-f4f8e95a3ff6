'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add new columns to User table
    await queryInterface.addColumn('User', 'website', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('User', 'description', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('User', 'isPrivate', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove the columns from User table
    await queryInterface.removeColumn('User', 'website');
    await queryInterface.removeColumn('User', 'description');
    await queryInterface.removeColumn('User', 'isPrivate');
  },
};
