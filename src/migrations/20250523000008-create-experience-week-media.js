/**
 * Migration: Create ExperienceWeekMedia table
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ExperienceWeekMedia', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      experienceWeekId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceWeek',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      type: {
        type: Sequelize.ENUM('IMAGE', 'VIDEO', 'DOCUMENT', 'LINK', 'AUDIO'),
        allowNull: false,
      },
      url: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      title: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add indexes
    await queryInterface.addIndex('ExperienceWeekMedia', ['experienceWeekId'], {
      name: 'experience_week_media_week_id_idx',
    });

    await queryInterface.addIndex('ExperienceWeekMedia', ['type'], {
      name: 'experience_week_media_type_idx',
    });

    await queryInterface.addIndex(
      'ExperienceWeekMedia',
      ['experienceWeekId', 'order'],
      {
        name: 'experience_week_media_order_idx',
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ExperienceWeekMedia');
  },
};
