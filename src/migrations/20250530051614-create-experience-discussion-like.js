'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ExperienceDiscussionLike', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      discussionId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceDiscussion',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      likedBy: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add indexes
    await queryInterface.addIndex(
      'ExperienceDiscussionLike',
      ['discussionId'],
      {
        name: 'experience_discussion_like_discussion_id_idx',
      }
    );

    await queryInterface.addIndex('ExperienceDiscussionLike', ['likedBy'], {
      name: 'experience_discussion_like_liked_by_idx',
    });

    // Add unique constraint for discussionId and likedBy
    await queryInterface.addIndex(
      'ExperienceDiscussionLike',
      ['discussionId', 'likedBy'],
      {
        name: 'experience_discussion_like_unique_idx',
        unique: true,
      }
    );
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex(
      'ExperienceDiscussionLike',
      'experience_discussion_like_discussion_id_idx'
    );
    await queryInterface.removeIndex(
      'ExperienceDiscussionLike',
      'experience_discussion_like_liked_by_idx'
    );
    await queryInterface.removeIndex(
      'ExperienceDiscussionLike',
      'experience_discussion_like_unique_idx'
    );

    // Then remove the table
    await queryInterface.dropTable('ExperienceDiscussionLike');
  },
};
