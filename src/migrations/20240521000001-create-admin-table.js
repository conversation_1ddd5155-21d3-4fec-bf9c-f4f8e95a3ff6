'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Admin', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      password: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    // Add a hook to convert email to lowercase
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION convert_email_to_lowercase()
      RET<PERSON>NS trigger AS $$
      BEGIN
        NEW.email = LOWER(NEW.email);
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      DROP TRIGGER IF EXISTS admin_email_lowercase ON "Admin";
      
      CREATE TRIGGER admin_email_lowercase
      BEFORE INSERT OR UPDATE ON "Admin"
      FOR EACH ROW
      EXECUTE FUNCTION convert_email_to_lowercase();
    `);
  },

  async down(queryInterface, Sequelize) {
    // Drop the trigger and function first
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS admin_email_lowercase ON "Admin";
      DROP FUNCTION IF EXISTS convert_email_to_lowercase();
    `);

    // Then drop the table
    await queryInterface.dropTable('Admin');
  },
};
