'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('UserFocus', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      focusId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Focus',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Add unique constraint to prevent duplicate user-focus combinations
    await queryInterface.addIndex('UserFocus', ['userId', 'focusId'], {
      unique: true,
      name: 'user_focus_unique',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('UserFocus');
  },
};
