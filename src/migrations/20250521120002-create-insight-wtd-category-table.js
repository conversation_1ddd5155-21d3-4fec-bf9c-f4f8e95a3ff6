'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('InsightWtdCategory', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      insightId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Insight',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      wtdCategoryId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'WtdCategory',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    // Add a unique constraint to prevent duplicate associations
    await queryInterface.addConstraint('InsightWtdCategory', {
      fields: ['insightId', 'wtdCategoryId'],
      type: 'unique',
      name: 'unique_insight_wtd_category',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('InsightWtdCategory');
  },
};
