/**
 * Admin Auth Controller
 *
 * Handles admin authentication-related HTTP requests
 */
const authService = require('./auth.service');
const { ApiResponse } = require('@utils/response.utils');
const { ADMIN } = require('@utils/messages.utils');

/**
 * Admin auth controller
 */
const authController = {
  /**
   * Login admin
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  login: async (req, res, next) => {
    try {
      const { email, password } = req.body;

      // Login admin using the auth service
      const adminData = await authService.loginAdmin(email, password);

      return ApiResponse.success(res, ADMIN.LOGIN_SUCCESS, adminData);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get admin profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getProfile: async (req, res, next) => {
    try {
      const adminId = req.user.id;

      // Get admin profile using the auth service
      const admin = await authService.getAdminProfile(adminId);

      return ApiResponse.success(res, ADMIN.PROFILE_RETRIEVED, admin);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = authController;
