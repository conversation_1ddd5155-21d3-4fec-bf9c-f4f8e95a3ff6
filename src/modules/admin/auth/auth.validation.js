/**
 * Admin Auth Validation Schemas
 *
 * Defines validation schemas for admin authentication-related requests
 */
const { body } = require('express-validator');
const { VALIDATION } = require('@utils/messages.utils');

/**
 * Admin auth validation schemas
 */
const authValidation = {
  /**
   * Admin login validation schema
   */
  login: [
    body('email').isEmail().withMessage(VALIDATION.EMAIL_INVALID),
    body('password').notEmpty().withMessage(VALIDATION.REQUIRED),
  ],
};

module.exports = authValidation;
