/**
 * Focus Module
 *
 * This module handles Focus-related functionality
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const focusController = require('./focus.controller');
const focusValidation = require('./focus.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Get all focus areas
  router.get(
    '/',
    authenticate,
    validate(focusValidation.getAll),
    focusController.getAllFocusAreas
  );

  // Get a focus by ID
  router.get(
    '/:id',
    authenticate,
    validate(focusValidation.getById),
    focusController.getFocusById
  );

  // Create a new focus
  router.post(
    '/',
    authenticate,
    validate(focusValidation.create),
    focusController.createFocus
  );

  // Update a focus
  router.put(
    '/:id',
    authenticate,
    validate(focusValidation.update),
    focusController.updateFocus
  );

  // Delete a focus
  router.delete(
    '/:id',
    authenticate,
    validate(focusValidation.delete),
    focusController.deleteFocus
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
