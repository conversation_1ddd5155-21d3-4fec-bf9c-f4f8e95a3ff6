/**
 * Focus Validation
 *
 * Validation schemas for Focus operations
 */
const { body, param, query } = require('express-validator');

/**
 * Focus validation schemas
 */
const focusValidation = {
  /**
   * Create focus validation schema
   */
  create: [body('name').notEmpty().withMessage('Focus name is required')],

  /**
   * Update focus validation schema
   */
  update: [
    param('id').isUUID().withMessage('Invalid focus ID'),
    body('name').notEmpty().withMessage('Focus name is required'),
  ],

  /**
   * Delete focus validation schema
   */
  delete: [param('id').isUUID().withMessage('Invalid focus ID')],

  /**
   * Get focus by ID validation schema
   */
  getById: [param('id').isUUID().withMessage('Invalid focus ID')],

  /**
   * Get all focus areas validation schema
   */
  getAll: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
      .toInt(),
    query('search')
      .optional()
      .isString()
      .withMessage('Search must be a string'),
  ],
};

module.exports = focusValidation;
