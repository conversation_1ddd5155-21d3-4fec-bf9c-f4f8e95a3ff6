/**
 * Focus Controller
 *
 * Handles Focus-related HTTP requests
 */
const focusService = require('./focus.service');
const { ApiResponse } = require('@utils/response.utils');
const { FOCUS } = require('@utils/messages.utils');

/**
 * Focus controller
 */
const focusController = {
  /**
   * Get all focus areas with pagination and optional search
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllFocusAreas: async (req, res, next) => {
    try {
      // Extract query parameters
      const { page = 1, limit = 10, search = '' } = req.query;

      // Get focus areas with pagination
      const result = await focusService.getAllFocusAreas({
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
      });

      return ApiResponse.success(res, FOCUS.ALL_RETRIEVED, result.focus, {
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get focus by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getFocusById: async (req, res, next) => {
    try {
      const { id } = req.params;
      const focus = await focusService.getFocusById(id);
      return ApiResponse.success(res, FOCUS.RETRIEVED, focus);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Create a new focus
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  createFocus: async (req, res, next) => {
    try {
      const { name } = req.body;
      const focus = await focusService.createFocus({ name });
      return ApiResponse.created(res, FOCUS.CREATED, focus);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update focus
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  updateFocus: async (req, res, next) => {
    try {
      const { id } = req.params;
      const { name } = req.body;
      const focus = await focusService.updateFocus(id, { name });
      return ApiResponse.success(res, FOCUS.UPDATED, focus);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete focus
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  deleteFocus: async (req, res, next) => {
    try {
      const { id } = req.params;
      await focusService.deleteFocus(id);
      return ApiResponse.success(res, FOCUS.DELETED);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = focusController;
