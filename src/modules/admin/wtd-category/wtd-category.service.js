/**
 * WTD Category Service
 *
 * Handles WTD category-related business logic
 */
const wtdCategoryRepository = require('@models/repositories/wtd-category.repository');

/**
 * WTD Category service
 */
const wtdCategoryService = {
  /**
   * Get all WTD categories with pagination and optional search
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for name
   * @returns {Promise<Object>} Object containing categories and pagination info
   */
  getAllCategories: async ({ page, limit, search } = {}) => {
    try {
      return await wtdCategoryRepository.findAll({ page, limit, search });
    } catch (error) {
      console.error('Error in getAllCategories service:', error);
      throw error;
    }
  },

  /**
   * Get WTD category by ID
   * @param {string} id - WTD category ID
   * @returns {Promise<Object>} WTD category data
   */
  getCategoryById: async (id) => {
    try {
      return await wtdCategoryRepository.findById(id);
    } catch (error) {
      console.error('Error in getCategoryById service:', error);
      throw error;
    }
  },

  /**
   * Create a new WTD category
   * @param {Object} data - WTD category data
   * @returns {Promise<Object>} Created WTD category
   */
  createCategory: async (data) => {
    try {
      return await wtdCategoryRepository.create(data);
    } catch (error) {
      console.error('Error in createCategory service:', error);
      throw error;
    }
  },

  /**
   * Update WTD category
   * @param {string} id - WTD category ID
   * @param {Object} data - Data to update
   * @returns {Promise<Object>} Updated WTD category
   */
  updateCategory: async (id, data) => {
    try {
      return await wtdCategoryRepository.update(id, data);
    } catch (error) {
      console.error('Error in updateCategory service:', error);
      throw error;
    }
  },

  /**
   * Delete WTD category
   * @param {string} id - WTD category ID
   * @returns {Promise<boolean>} True if deleted
   */
  deleteCategory: async (id) => {
    try {
      return await wtdCategoryRepository.delete(id);
    } catch (error) {
      console.error('Error in deleteCategory service:', error);
      throw error;
    }
  },
};

module.exports = wtdCategoryService;
