/**
 * WTD Category Module
 *
 * This module handles WTD category-related functionality
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const wtdCategoryController = require('./wtd-category.controller');
const wtdCategoryValidation = require('./wtd-category.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Get all WTD categories
  router.get(
    '/',
    authenticate,
    validate(wtdCategoryValidation.getAll),
    wtdCategoryController.getAllCategories
  );

  // Get a WTD category by ID
  router.get(
    '/:id',
    authenticate,
    validate(wtdCategoryValidation.getById),
    wtdCategoryController.getCategoryById
  );

  // Create a new WTD category
  router.post(
    '/',
    authenticate,
    validate(wtdCategoryValidation.create),
    wtdCategoryController.createCategory
  );

  // Update a WTD category
  router.put(
    '/:id',
    authenticate,
    validate(wtdCategoryValidation.update),
    wtdCategoryController.updateCategory
  );

  // Delete a WTD category
  router.delete(
    '/:id',
    authenticate,
    validate(wtdCategoryValidation.delete),
    wtdCategoryController.deleteCategory
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
