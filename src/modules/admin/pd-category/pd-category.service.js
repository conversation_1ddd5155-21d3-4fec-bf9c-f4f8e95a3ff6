/**
 * PD Category Service
 *
 * Handles PD category-related business logic
 */
const pdCategoryRepository = require('@models/repositories/pd-category.repository');

/**
 * PD Category service
 */
const pdCategoryService = {
  /**
   * Get all PD categories with pagination and optional search
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for name
   * @returns {Promise<Object>} Object containing categories and pagination info
   */
  getAllCategories: async ({ page, limit, search } = {}) => {
    try {
      return await pdCategoryRepository.findAll({ page, limit, search });
    } catch (error) {
      console.error('Error in getAllCategories service:', error);
      throw error;
    }
  },

  /**
   * Get PD category by ID
   * @param {string} id - PD category ID
   * @returns {Promise<Object>} PD category data
   */
  getCategoryById: async (id) => {
    try {
      return await pdCategoryRepository.findById(id);
    } catch (error) {
      console.error('Error in getCategoryById service:', error);
      throw error;
    }
  },

  /**
   * Create a new PD category
   * @param {Object} data - PD category data
   * @returns {Promise<Object>} Created PD category
   */
  createCategory: async (data) => {
    try {
      return await pdCategoryRepository.create(data);
    } catch (error) {
      console.error('Error in createCategory service:', error);
      throw error;
    }
  },

  /**
   * Update PD category
   * @param {string} id - PD category ID
   * @param {Object} data - Data to update
   * @returns {Promise<Object>} Updated PD category
   */
  updateCategory: async (id, data) => {
    try {
      return await pdCategoryRepository.update(id, data);
    } catch (error) {
      console.error('Error in updateCategory service:', error);
      throw error;
    }
  },

  /**
   * Delete PD category
   * @param {string} id - PD category ID
   * @returns {Promise<boolean>} True if deleted
   */
  deleteCategory: async (id) => {
    try {
      return await pdCategoryRepository.delete(id);
    } catch (error) {
      console.error('Error in deleteCategory service:', error);
      throw error;
    }
  },
};

module.exports = pdCategoryService;
