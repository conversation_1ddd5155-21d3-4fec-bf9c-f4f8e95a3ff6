/**
 * PD Category Validation
 *
 * Validation schemas for PD category operations
 */
const { body, param, query } = require('express-validator');

/**
 * PD Category validation schemas
 */
const pdCategoryValidation = {
  /**
   * Create PD category validation schema
   */
  create: [body('name').notEmpty().withMessage('Category name is required')],

  /**
   * Update PD category validation schema
   */
  update: [
    param('id').isUUID().withMessage('Invalid category ID format'),
    body('name').notEmpty().withMessage('Category name is required'),
  ],

  /**
   * Delete PD category validation schema
   */
  delete: [param('id').isUUID().withMessage('Invalid category ID format')],

  /**
   * Get PD category by ID validation schema
   */
  getById: [param('id').isUUID().withMessage('Invalid category ID format')],

  /**
   * Get all PD categories validation schema
   */
  getAll: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
      .toInt(),
    query('search')
      .optional()
      .isString()
      .withMessage('Search must be a string'),
  ],
};

module.exports = pdCategoryValidation;
