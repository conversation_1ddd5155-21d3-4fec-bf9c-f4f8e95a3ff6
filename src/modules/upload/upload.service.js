/**
 * Upload Service
 *
 * Handles file upload-related business logic
 */
const {
  S3Client,
  PutObjectCommand,
  HeadObjectCommand,
} = require('@aws-sdk/client-s3');
const { v4: uuidv4 } = require('uuid');
const { ApiException } = require('@utils/exception.utils');
const { UPLOAD } = require('@utils/messages.utils');
const { HttpStatus } = require('@utils/enums.utils');
require('dotenv').config();

const {
  AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY,
  AWS_S3_BUCKET,
  AWS_REGION,
  CDN_WEB_STATIC,
} = process.env;

/**
 * Upload service
 */
class UploadService {
  constructor() {
    this.s3Client = new S3Client({
      credentials: {
        accessKeyId: AWS_ACCESS_KEY_ID,
        secretAccessKey: AWS_SECRET_ACCESS_KEY,
      },
      region: AWS_REGION,
    });
  }

  /**
   * Generate a unique file name
   * @param {string} fileName - Original file name
   * @returns {string} Generated file name
   */
  _generateFileName(fileName) {
    const currentTimeInMillis = Date.now();
    const rawFileName = fileName.replace(/\s/g, '');
    const parts = rawFileName.split('.');
    const ext = parts.pop();
    const name = parts.join('.');
    const cleanName = name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
    return `${cleanName}_${currentTimeInMillis}.${ext}`;
  }

  /**
   * Upload a file to S3
   * @param {Object} file - File object from multer
   * @param {string} file.originalname - Original file name
   * @param {Buffer} file.buffer - File buffer
   * @param {string} file.mimetype - File MIME type
   * @returns {Promise<Object>} Upload result with key and URL
   * @throws {ApiException} If upload fails
   */
  async uploadFile(file) {
    try {
      const { originalname, buffer, mimetype } = file;
      const fileName = this._generateFileName(originalname);
      const s3Key = `uploads/${fileName}`;

      const command = new PutObjectCommand({
        Bucket: AWS_S3_BUCKET,
        Key: s3Key,
        Body: buffer,
        ContentType: mimetype,
      });

      await this.s3Client.send(command);

      return {
        message: UPLOAD.FILE_UPLOADED,
        key: s3Key,
        fileUrl: `${CDN_WEB_STATIC}/${s3Key}`,
      };
    } catch (error) {
      console.error('Error in uploadFile service:', error);
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        UPLOAD.UPLOAD_FAILED
      );
    }
  }

  /**
   * Get file URL by key
   * @param {string} key - File key in S3
   * @returns {Promise<Object>} File URL
   * @throws {ApiException} If file not found or access fails
   */
  async getFileByKey(key) {
    try {
      const command = new HeadObjectCommand({
        Bucket: AWS_S3_BUCKET,
        Key: key,
      });

      await this.s3Client.send(command);
      return {
        message: UPLOAD.FILE_RETRIEVED,
        fileUrl: `${CDN_WEB_STATIC}/${key}`,
      };
    } catch (error) {
      console.error('Error in getFileByKey service:', error);
      if (error.name === 'NotFound') {
        throw new ApiException(HttpStatus.NOT_FOUND, UPLOAD.FILE_NOT_FOUND);
      }
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        UPLOAD.UPLOAD_FAILED
      );
    }
  }
}

module.exports = new UploadService();
