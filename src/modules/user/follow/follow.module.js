/**
 * Follow Module
 *
 * This module handles user following functionality
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const followController = require('./follow.controller');
const followValidation = require('./follow.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Toggle follow status for a user
  router.post(
    '/:followingId',
    authenticate,
    validate(followValidation.toggleFollow),
    followController.toggleFollow
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
