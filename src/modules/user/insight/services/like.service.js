/**
 * Like Service
 *
 * Handles business logic for liking insights
 */
const likedInsightRepository = require('@models/repositories/liked-insight.repository');

/**
 * Like Service
 */
const likeService = {
  /**
   * Toggle like status for an insight
   * @param {string} userId - User ID
   * @param {string} insightId - Insight ID
   * @returns {Promise<Object>} Object with isLiked status
   */
  toggleLike: async (userId, insightId) => {
    try {
      return await likedInsightRepository.toggleLike(userId, insightId);
    } catch (error) {
      throw error;
    }
  },
};

module.exports = likeService;
