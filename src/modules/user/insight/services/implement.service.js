/**
 * Implement Service
 *
 * Handles business logic for implementing insights
 */
const implementedInsightRepository = require('@models/repositories/implemented-insight.repository');

/**
 * Implement Service
 */
const implementService = {
  /**
   * Toggle implement status for an insight
   * @param {string} userId - User ID
   * @param {string} insightId - Insight ID
   * @returns {Promise<Object>} Object with isImplemented status
   */
  toggleImplement: async (userId, insightId) => {
    try {
      return await implementedInsightRepository.toggleImplement(
        userId,
        insightId
      );
    } catch (error) {
      throw error;
    }
  },
};

module.exports = implementService;
