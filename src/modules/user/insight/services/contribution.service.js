/**
 * Contribution Service
 * Handles contribution (comments) business logic for insights
 */
const contributionRepository = require('@models/repositories/contribution.repository');
const insightRepository = require('@models/repositories/insight.repository');
const { ApiException } = require('@utils/exception.utils');

/**
 * Contribution service
 */
const contributionService = {
  /**
   * Add a contribution (comment) to an insight
   * @param {string} insightId - The insight ID
   * @param {string} userId - The user ID
   * @param {string} content - The contribution content
   * @returns {Object} Created contribution with user details
   */
  async addContribution(insightId, userId, content) {
    try {
      // Check if insight exists
      const insight = await insightRepository.findById(insightId);
      if (!insight) {
        throw new ApiException(404, 'Insight not found');
      }

      // Create contribution using repository
      const contribution = await contributionRepository.create({
        insightId,
        contributedBy: userId,
        content,
      });

      return contribution;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all contributions for an insight with pagination
   * @param {string} insightId - The insight ID
   * @param {Object} options - Pagination and sorting options
   * @param {string} userId - Current user ID for like status
   * @returns {Object} Contributions with pagination info
   */
  async getContributionsByInsight(insightId, options = {}, userId = null) {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'DESC',
      } = options;

      // Check if insight exists
      const insight = await insightRepository.findById(insightId);
      if (!insight) {
        throw new ApiException(404, 'Insight not found');
      }

      // Get contributions using repository
      const result = await contributionRepository.findByInsight(insightId, {
        page,
        limit,
        orderBy: sortBy,
        orderDirection: sortOrder,
      });

      // Add user's like status to each contribution
      const contributionsWithUserStatus = result.contributions.map(
        (contribution) => {
          const contributionData = contribution.toJSON
            ? contribution.toJSON()
            : contribution;

          // Calculate likes count
          contributionData.likesCount = contributionData.likes
            ? contributionData.likes.length
            : 0;

          // Check if current user liked this contribution
          contributionData.isLiked =
            userId && contributionData.likes
              ? contributionData.likes.some((like) => like.likedBy === userId)
              : false;

          // Remove the likes array to keep response clean
          delete contributionData.contributedBy;
          delete contributionData.likes;

          return contributionData;
        }
      );

      return {
        contributions: contributionsWithUserStatus,
        pagination: result.pagination,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Toggle like/unlike for a contribution
   * @param {string} contributionId - The contribution ID
   * @param {string} userId - The user ID
   * @returns {Object} Like status and count
   */
  async toggleContributionLike(contributionId, userId) {
    try {
      // Check if contribution exists
      const contribution = await contributionRepository.findById(
        contributionId,
        false
      );
      if (!contribution) {
        throw new ApiException(404, 'Contribution not found');
      }

      // Toggle like using repository
      const result = await contributionRepository.toggleLike(
        contributionId,
        userId
      );

      return {
        isLiked: result.liked,
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Delete a contribution (user can only delete their own)
   * @param {string} contributionId - The contribution ID
   * @param {string} userId - The user ID
   * @returns {boolean} True if deleted
   */
  async deleteContribution(contributionId, userId) {
    try {
      // Check if contribution exists
      const contribution = await contributionRepository.findById(
        contributionId,
        false
      );
      if (!contribution) {
        throw new ApiException(404, 'Contribution not found');
      }

      // Check if user owns this contribution
      if (contribution.contributedBy !== userId) {
        throw new ApiException(
          403,
          'You can only delete your own contributions'
        );
      }

      // Delete the contribution
      return await contributionRepository.delete(contributionId);
    } catch (error) {
      throw error;
    }
  },
};

module.exports = contributionService;
