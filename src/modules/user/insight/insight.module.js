/**
 * User Insight Module
 *
 * This module handles insight-related functionality for users
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const { requireProviderPlus } = require('@middlewares/role.middleware');
const { parseArrayParams } = require('@middlewares/query-parser.middleware');
const paginationMiddleware = require('@middlewares/pagination.middleware');
const insightController = require('./insight.controller');
const insightValidation = require('./insight.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Create a new insight - only for provider plus users
  router.post(
    '/',
    authenticate,
    requireProviderPlus,
    validate(insightValidation.create),
    insightController.createInsight
  );

  // Get all insights - accessible to all authenticated users
  router.get(
    '/',
    authenticate,
    parseArrayParams(['focusIds', 'pdCategoryIds', 'wtdCategoryIds']),
    paginationMiddleware,
    validate(insightValidation.getAll),
    insightController.getAllInsights
  );

  // Get trending insights - accessible to all authenticated users
  router.get(
    '/trending',
    authenticate,
    paginationMiddleware,
    validate(insightValidation.getTrending),
    insightController.getTrendingInsights
  );

  // Get PD Spotlight insights (Classroom Management category) - accessible to all authenticated users
  router.get(
    '/pd-spotlight',
    authenticate,
    paginationMiddleware,
    validate(insightValidation.getPDSpotlight),
    insightController.getPDSpotlight
  );

  // Get all bookmarked insights - accessible to all authenticated users
  router.get(
    '/bookmarks',
    authenticate,
    paginationMiddleware,
    validate(insightValidation.getBookmarkedInsights),
    insightController.getBookmarkedInsights
  );

  // Get an insight by ID - accessible to all authenticated users
  router.get(
    '/:id',
    authenticate,
    validate(insightValidation.getById),
    insightController.getInsightById
  );

  // Toggle bookmark status for an insight - accessible to all authenticated users
  router.post(
    '/:insightId/bookmark',
    authenticate,
    validate(insightValidation.toggleBookmark),
    insightController.toggleBookmark
  );

  // Toggle like status for an insight - accessible to all authenticated users
  router.post(
    '/:insightId/like',
    authenticate,
    validate(insightValidation.toggleLike),
    insightController.toggleLike
  );

  // Toggle implement status for an insight - accessible to all authenticated users
  router.post(
    '/:insightId/implement',
    authenticate,
    validate(insightValidation.toggleImplement),
    insightController.toggleImplement
  );

  // Add a contribution (comment) to an insight
  router.post(
    '/:insightId/contribution',
    authenticate,
    validate(insightValidation.addContribution),
    insightController.addContribution
  );

  // Get all contributions for an insight
  router.get(
    '/:insightId/contribution',
    authenticate,
    paginationMiddleware,
    validate(insightValidation.getContributionsByInsight),
    insightController.getContributionsByInsight
  );

  // Toggle like status for a contribution
  router.post(
    '/contribution/:contributionId',
    authenticate,
    validate(insightValidation.toggleContributionLike),
    insightController.toggleContributionLike
  );

  // Report an insight
  router.post(
    '/:insightId/report',
    authenticate,
    validate(insightValidation.reportInsight),
    insightController.reportInsight
  );

  // Report a contribution
  router.post(
    '/contribution/:contributionId/report',
    authenticate,
    validate(insightValidation.reportContribution),
    insightController.reportContribution
  );

  // Delete a contribution (user can only delete their own)
  router.delete(
    '/contribution/:contributionId',
    authenticate,
    validate(insightValidation.deleteContribution),
    insightController.deleteContribution
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
