/**
 * Experience Controller
 *
 * Handles experience-related HTTP requests
 */
const experienceService = require('./services/experience.service');
const experienceEnrollmentService = require('./services/experience-enrollment.service');
const experienceReviewService = require('./services/experience-review.service');
const experienceDiscussionService = require('./services/experience-discussion.service');
const { ApiResponse } = require('@utils/response.utils');
const {
  EXPERIENCE,
  EXPERIENCE_ENROLLMENT,
  EXPERIENCE_REVIEW,
} = require('@utils/messages.utils');

/**
 * Experience controller
 */
const experienceController = {
  /**
   * Create a new experience
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  createExperience: async (req, res, next) => {
    try {
      const data = req.body;
      const userId = req.user.id;

      const experience = await experienceService.createExperience(data, userId);

      return ApiResponse.created(res, EXPERIENCE.CREATED, experience);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all experiences with pagination
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllExperiences: async (req, res, next) => {
    try {
      const { myExperiences } = req.query;
      const { page, limit } = req.pagination;
      const userId = req.user.id;

      const result = await experienceService.getAllExperiences({
        page,
        limit,
        createdBy: myExperiences === 'true' ? req.user.id : undefined,
        userId,
      });

      return ApiResponse.success(
        res,
        EXPERIENCE.ALL_RETRIEVED,
        result.experiences,
        { pagination: result.pagination }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get complete experience details
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getExperienceDetails: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const userId = req.user.id;

      const experience = await experienceService.getExperienceDetails(
        experienceId,
        userId
      );

      return ApiResponse.success(res, EXPERIENCE.RETRIEVED, experience);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Enroll a user in an experience
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  enrollUser: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const { startDate } = req.body;
      const userId = req.user.id;

      const enrollment = await experienceEnrollmentService.enrollUser(
        experienceId,
        userId,
        startDate
      );

      return ApiResponse.created(
        res,
        EXPERIENCE_ENROLLMENT.CREATED,
        enrollment
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Complete a specific week for a user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  completeWeek: async (req, res, next) => {
    try {
      const { experienceId, weekNumber } = req.params;
      const userId = req.user.id;

      const result = await experienceEnrollmentService.completeWeek(
        experienceId,
        userId,
        parseInt(weekNumber)
      );

      return ApiResponse.success(
        res,
        EXPERIENCE_ENROLLMENT.WEEK_COMPLETED,
        result
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get detailed progress for an enrollment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getEnrollmentProgress: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const userId = req.user.id;

      const progress = await experienceEnrollmentService.getEnrollmentProgress(
        experienceId,
        userId
      );

      return ApiResponse.success(
        res,
        EXPERIENCE_ENROLLMENT.PROGRESS_RETRIEVED,
        progress
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Submit an experience review
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  submitExperienceReview: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const userId = req.user.id;

      const review = await experienceReviewService.submitReview(
        experienceId,
        userId,
        req.body
      );

      return ApiResponse.created(res, EXPERIENCE_REVIEW.CREATED, review);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get reviews by experience ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getExperienceReviews: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const { page, limit } = req.pagination;

      const result = await experienceReviewService.getReviewsByExperienceId({
        experienceId,
        page,
        limit,
      });

      return ApiResponse.success(
        res,
        EXPERIENCE_REVIEW.RETRIEVED,
        result.data,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Create a new discussion
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  createDiscussion: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const { content } = req.body;
      const userId = req.user.id;

      const discussion = await experienceDiscussionService.createDiscussion(
        experienceId,
        userId,
        content
      );

      return ApiResponse.created(
        res,
        'Discussion created successfully',
        discussion
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get discussions by experience ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getDiscussionsByExperienceId: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const { page, limit } = req.pagination;
      const userId = req.user.id;

      const result =
        await experienceDiscussionService.getDiscussionsByExperienceId({
          experienceId,
          page,
          limit,
          userId,
        });

      return ApiResponse.success(
        res,
        'Discussions retrieved successfully',
        result.discussions,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Toggle like status for a discussion
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  toggleDiscussionLike: async (req, res, next) => {
    try {
      const { discussionId } = req.params;
      const userId = req.user.id;

      const result = await experienceDiscussionService.toggleLike(
        discussionId,
        userId
      );

      return ApiResponse.success(
        res,
        result.isLiked
          ? 'Discussion liked successfully'
          : 'Discussion unliked successfully',
        result
      );
    } catch (error) {
      next(error);
    }
  },
};

module.exports = experienceController;
