/**
 * Experience Review Service
 *
 * Handles business logic for experience reviews
 */
const experienceReviewRepository = require('@models/repositories/experience-review.repository');
const experienceRepository = require('@models/repositories/experience.repository');
const experienceEnrollmentRepository = require('@models/repositories/experience-enrollment.repository');
const { ApiException } = require('@utils/exception.utils');
const { EXPERIENCE_REVIEW } = require('@utils/messages.utils');
const {
  HttpStatus,
  ExperienceEnrollmentStatus,
} = require('@utils/enums.utils');

/**
 * Experience Review Service
 */
const experienceReviewService = {
  /**
   * Submit a review for an experience
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {Object} reviewData - Review data from request body
   * @returns {Promise<Object>} Created review
   */
  submitReview: async (experienceId, userId, reviewData) => {
    try {
      // Check if user is the creator of the experience
      const creatorId = await experienceRepository.findCreatorId(experienceId);
      if (!creatorId) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_REVIEW.EXPERIENCE_NOT_FOUND
        );
      }

      if (creatorId === userId) {
        throw new ApiException(
          HttpStatus.FORBIDDEN,
          EXPERIENCE_REVIEW.CANNOT_REVIEW_OWN_EXPERIENCE
        );
      }

      // Check if user has completed the experience
      const enrollment =
        await experienceEnrollmentRepository.findByExperienceAndUser(
          experienceId,
          userId
        );
      if (!enrollment) {
        throw new ApiException(
          HttpStatus.FORBIDDEN,
          EXPERIENCE_REVIEW.NOT_ENROLLED
        );
      }
      if (enrollment.status !== ExperienceEnrollmentStatus.COMPLETED) {
        throw new ApiException(
          HttpStatus.FORBIDDEN,
          EXPERIENCE_REVIEW.MUST_COMPLETE_EXPERIENCE
        );
      }

      // Check if user has already reviewed this experience
      const existingReview =
        await experienceReviewRepository.findByExperienceAndUser(
          experienceId,
          userId
        );
      if (existingReview) {
        throw new ApiException(
          HttpStatus.CONFLICT,
          EXPERIENCE_REVIEW.ALREADY_REVIEWED
        );
      }

      // Create the review
      const review = await experienceReviewRepository.create({
        experienceId,
        userId,
        ...reviewData,
      });

      return review;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get reviews by experience ID with pagination
   * @param {string} experienceId - Experience ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Reviews with pagination
   */
  getReviewsByExperienceId: async (experienceId, page, limit) => {
    try {
      // Verify experience exists
      const experience = await experienceRepository.findById(experienceId);
      if (!experience) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          EXPERIENCE_REVIEW.EXPERIENCE_NOT_FOUND
        );
      }

      return await experienceReviewRepository.findByExperienceId(
        experienceId,
        page,
        limit
      );
    } catch (error) {
      throw error;
    }
  },
};

module.exports = experienceReviewService;
