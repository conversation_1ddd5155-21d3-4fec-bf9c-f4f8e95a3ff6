/**
 * Experience Discussion Service
 *
 * Business logic for experience discussion operations
 */
const experienceRepository = require('@models/repositories/experience.repository');
const experienceDiscussionRepository = require('@models/repositories/experience-discussion.repository');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { EXPERIENCE } = require('@utils/messages.utils');

/**
 * Experience discussion service
 */
const experienceDiscussionService = {
  /**
   * Create a new discussion for an experience
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {string} content - Discussion content
   * @returns {Promise<Object>} Created discussion
   */
  createDiscussion: async (experienceId, userId, content) => {
    try {
      // Verify experience exists
      const experience = await experienceRepository.findById(experienceId);
      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      const discussionData = {
        content,
        experienceId,
        createdBy: userId,
      };

      return await experienceDiscussionRepository.createDiscussion(
        discussionData
      );
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get discussions by experience ID with pagination
   * @param {string} experienceId - Experience ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @param {string} userId - User ID for like status
   * @returns {Promise<Object>} Discussions with pagination
   */
  getDiscussionsByExperienceId: async (experienceId, page, limit, userId) => {
    try {
      // Verify experience exists
      const experience = await experienceRepository.findById(experienceId);
      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      return await experienceDiscussionRepository.findDiscussionsByExperienceId(
        experienceId,
        page,
        limit,
        userId
      );
    } catch (error) {
      throw error;
    }
  },

  /**
   * Toggle like status for a discussion
   * @param {string} discussionId - Discussion ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated discussion with like status
   */
  toggleLike: async (discussionId, userId) => {
    try {
      return await experienceDiscussionRepository.toggleDiscussionLike(
        discussionId,
        userId
      );
    } catch (error) {
      throw error;
    }
  },
};

module.exports = experienceDiscussionService;
