/**
 * Experience Enrollment Service
 *
 * Handles business logic for experience enrollments
 */
const experienceEnrollmentRepository = require('@models/repositories/experience-enrollment.repository');

/**
 * Experience Enrollment Service
 */
const experienceEnrollmentService = {
  /**
   * Enroll a user in an experience
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {Object} enrollmentData - Enrollment data from request body
   * @returns {Promise<Object>} Created enrollment
   */
  async enrollUser(experienceId, userId, enrollmentData) {
    try {
      const data = {
        experienceId,
        userId,
        ...enrollmentData,
      };
      return await experienceEnrollmentRepository.enrollUser(data);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Complete a specific week for a user
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {number} weekNumber - Week number to complete
   * @returns {Promise<Object>} Updated enrollment with progress
   */
  async completeWeek(experienceId, userId, weekNumber) {
    try {
      return await experienceEnrollmentRepository.completeWeek(
        experienceId,
        userId,
        weekNumber
      );
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get detailed progress for an enrollment
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Detailed progress information
   */
  async getEnrollmentProgress(experienceId, userId) {
    try {
      return await experienceEnrollmentRepository.getEnrollmentProgress(
        experienceId,
        userId
      );
    } catch (error) {
      throw error;
    }
  },
};

module.exports = experienceEnrollmentService;
