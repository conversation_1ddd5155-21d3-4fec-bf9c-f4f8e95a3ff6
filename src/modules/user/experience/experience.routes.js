router.post(
  '/:experienceId/review',
  authMiddleware,
  experienceValidation.submitReview,
  experienceController.submitExperienceReview
);

router.get(
  '/:experienceId/review',
  authMiddleware,
  paginationMiddleware,
  experienceController.getExperienceReviews
);

router.post(
  '/:experienceId/discussion',
  authMiddleware,
  experienceValidation.createDiscussion,
  experienceController.createDiscussion
);
