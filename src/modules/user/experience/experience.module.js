/**
 * Experience Module
 *
 * This module handles experience-related functionality for users
 */
const express = require('express');
const router = express.Router();
const { authenticate } = require('@middlewares/auth.middleware');
const { requireProviderPlus } = require('@middlewares/role.middleware');
const { validate } = require('@middlewares/validation.middleware');
const experienceController = require('./experience.controller');
const experienceValidation = require('./experience.validation');
const paginationMiddleware = require('@middlewares/pagination.middleware');

/**
 * Register routes
 */
function registerRoutes() {
  // Create a new experience
  router.post(
    '/',
    authenticate,
    requireProviderPlus,
    validate(experienceValidation.create),
    experienceController.createExperience
  );

  // Get all experiences with pagination and search
  router.get(
    '/',
    authenticate,
    paginationMiddleware,
    validate(experienceValidation.getAll),
    experienceController.getAllExperiences
  );

  // Get experience details by ID
  router.get(
    '/:experienceId',
    authenticate,
    validate(experienceValidation.getById),
    experienceController.getExperienceDetails
  );

  // Enroll in an experience
  router.post(
    '/:experienceId/enroll',
    authenticate,
    validate(experienceValidation.enrollUser),
    experienceController.enrollUser
  );

  // Complete a specific week
  router.patch(
    '/:experienceId/week/:weekNumber/complete',
    authenticate,
    validate(experienceValidation.completeWeek),
    experienceController.completeWeek
  );

  // Get enrollment progress
  router.get(
    '/:experienceId/progress',
    authenticate,
    validate(experienceValidation.getById),
    experienceController.getEnrollmentProgress
  );

  // Submit a review for an experience
  router.post(
    '/:experienceId/review',
    authenticate,
    validate(experienceValidation.submitReview),
    experienceController.submitExperienceReview
  );

  // Discussion routes
  // Create a new discussion
  router.post(
    '/:experienceId/discussion',
    authenticate,
    validate(experienceValidation.createDiscussion),
    experienceController.createDiscussion
  );

  // Get discussions by experience ID
  router.get(
    '/:experienceId/discussion',
    authenticate,
    paginationMiddleware,
    validate(experienceValidation.getDiscussions),
    experienceController.getDiscussionsByExperienceId
  );

  // Toggle like status for a discussion
  router.post(
    '/discussion/:discussionId/like',
    authenticate,
    validate(experienceValidation.toggleDiscussionLike),
    experienceController.toggleDiscussionLike
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
