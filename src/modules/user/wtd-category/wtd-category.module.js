/**
 * User WTD Category Module
 *
 * This module handles WTD category-related functionality for regular users
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const userWtdCategoryController = require('./wtd-category.controller');
const wtdCategoryValidation = require('@admin/wtd-category/wtd-category.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Get all WTD categories
  router.get(
    '/',
    authenticate,
    validate(wtdCategoryValidation.getAll),
    userWtdCategoryController.getAllCategories
  );

  // Get a WTD category by ID
  router.get(
    '/:id',
    authenticate,
    validate(wtdCategoryValidation.getById),
    userWtdCategoryController.getCategoryById
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
