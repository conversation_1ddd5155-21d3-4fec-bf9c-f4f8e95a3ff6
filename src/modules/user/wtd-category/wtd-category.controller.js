/**
 * User WTD Category Controller
 *
 * Handles WTD category-related HTTP requests for regular users
 */
const wtdCategoryService = require('@admin/wtd-category/wtd-category.service');
const { ApiResponse } = require('@utils/response.utils');
const { WTD_CATEGORY } = require('@utils/messages.utils');

/**
 * User WTD Category controller
 */
const userWtdCategoryController = {
  /**
   * Get all WTD categories with pagination and optional search
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllCategories: async (req, res, next) => {
    try {
      // Extract query parameters
      const { page = 1, limit = 10, search = '' } = req.query;

      // Get categories with pagination
      const result = await wtdCategoryService.getAllCategories({
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
      });

      return ApiResponse.success(
        res,
        WTD_CATEGORY.ALL_RETRIEVED,
        result.categories,
        {
          pagination: result.pagination,
        }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get WTD category by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getCategoryById: async (req, res, next) => {
    try {
      const { id } = req.params;
      const category = await wtdCategoryService.getCategoryById(id);
      return ApiResponse.success(res, WTD_CATEGORY.RETRIEVED, category);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = userWtdCategoryController;
