/**
 * OnboardingConfig Module
 *
 * This module handles onboarding config-related functionality
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const onboardingConfigController = require('./onboarding-config.controller');
const onboardingConfigValidation = require('./onboarding-config.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Get all onboarding configs
  router.get(
    '/',
    validate(onboardingConfigValidation.getAll),
    onboardingConfigController.getAllOnboardingConfigs
  );

  // Get onboarding config by ID
  router.get(
    '/:id',
    validate(onboardingConfigValidation.getById),
    onboardingConfigController.getOnboardingConfigById
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
