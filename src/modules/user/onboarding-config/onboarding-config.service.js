/**
 * OnboardingConfig Service
 *
 * Handles onboarding config-related business logic
 */
const onboardingConfigRepository = require('@models/repositories/onboarding-config.repository');

/**
 * OnboardingConfig service
 */
const onboardingConfigService = {
  /**
   * Get all onboarding configs with pagination
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Onboarding configs and pagination info
   */
  getAllOnboardingConfigs: async ({ page, limit } = {}) => {
    try {
      return await onboardingConfigRepository.findAll({
        page,
        limit,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get onboarding config by ID
   * @param {string} id - Onboarding config ID
   * @returns {Promise<Object>} Onboarding config
   */
  getOnboardingConfigById: async (id) => {
    try {
      return await onboardingConfigRepository.findById(id);
    } catch (error) {
      throw error;
    }
  },
};

module.exports = onboardingConfigService;
