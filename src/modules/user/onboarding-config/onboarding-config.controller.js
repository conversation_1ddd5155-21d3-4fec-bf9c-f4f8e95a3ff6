/**
 * OnboardingConfig Controller
 *
 * Handles onboarding config-related HTTP requests
 */
const onboardingConfigService = require('./onboarding-config.service');
const { ApiResponse } = require('@utils/response.utils');
const { ONBOARDING_CONFIG } = require('@utils/messages.utils');

/**
 * OnboardingConfig controller
 */
const onboardingConfigController = {
  /**
   * Get all onboarding configs with pagination and optional search
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllOnboardingConfigs: async (req, res, next) => {
    try {
      // Extract query parameters
      const { page = 1, limit = 10, search = '' } = req.query;

      // Get configs with pagination
      const result = await onboardingConfigService.getAllOnboardingConfigs({
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
      });

      return ApiResponse.success(
        res,
        ONBOARDING_CONFIG.ALL_RETRIEVED,
        result.configs,
        {
          pagination: result.pagination,
        }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get onboarding config by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getOnboardingConfigById: async (req, res, next) => {
    try {
      const { id } = req.params;
      const config = await onboardingConfigService.getOnboardingConfigById(id);
      return ApiResponse.success(res, ONBOARDING_CONFIG.RETRIEVED, config);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = onboardingConfigController;
