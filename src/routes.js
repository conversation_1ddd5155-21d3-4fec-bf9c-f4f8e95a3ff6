/**
 * Main Routes File
 * This file combines all feature routes and applies appropriate prefixes
 */
const express = require('express');
const router = express.Router();

// Import admin feature routes
const adminAuthRoutes = require('@admin/auth/auth.module');
const adminPdCategoryRoutes = require('@admin/pd-category/pd-category.module');
const adminFocusRoutes = require('@admin/focus/focus.module');
const adminWtdCategoryRoutes = require('@admin/wtd-category/wtd-category.module');
const adminInsightRoutes = require('@admin/insight/insight.module');
const adminOnboardingConfigRoutes = require('@admin/onboarding-config/onboarding-config.module');

// Import user feature route
const userAuthRoutes = require('@user/auth/auth.module');
const userPdCategoryRoutes = require('@user/pd-category/pd-category.module');
const userFocusRoutes = require('@user/focus/focus.module');
const userWtdCategoryRoutes = require('@user/wtd-category/wtd-category.module');
const userInsightRoutes = require('@user/insight/insight.module');
const userExperienceRoutes = require('@user/experience/experience.module');
const userFollowRoutes = require('@user/follow/follow.module');
const userOnboardingConfigRoutes = require('@user/onboarding-config/onboarding-config.module');

// Import upload routes
const uploadRoutes = require('@modules/upload/upload.module');

/**
 * API Routes
 */
// Admin routes
router.use('/admin', adminAuthRoutes);
router.use('/admin/pd-categories', adminPdCategoryRoutes);
router.use('/admin/focus', adminFocusRoutes);
router.use('/admin/wtd-categories', adminWtdCategoryRoutes);
router.use('/admin/insights', adminInsightRoutes);
router.use('/admin/onboarding-config', adminOnboardingConfigRoutes);

// User routes
router.use('/user', userAuthRoutes);
router.use('/user/pd-categories', userPdCategoryRoutes);
router.use('/user/focus', userFocusRoutes);
router.use('/user/wtd-categories', userWtdCategoryRoutes);
router.use('/user/insights', userInsightRoutes);
router.use('/user/experience', userExperienceRoutes);
router.use('/user/follow', userFollowRoutes);
router.use('/user/onboarding-config', userOnboardingConfigRoutes);

// Upload routes
router.use('/upload', uploadRoutes);

/**
 * Health check route
 */
router.get('/health', (_, res) => {
  res.status(200).json({
    status: 200,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
  });
});

/**
 * Test routes for response format
 */
router.get('/test/success', (_, res) => {
  const { ApiResponse } = require('@utils/response.utils');
  return ApiResponse.success(res, 'Operation completed successfully', {
    id: 1,
    name: 'Test',
  });
});

router.get('/test/created', (_, res) => {
  const { ApiResponse } = require('@utils/response.utils');
  return ApiResponse.created(res, 'Resource created successfully', {
    id: 1,
    name: 'Test',
  });
});

router.get('/test/error', (_, res) => {
  const { ApiResponse } = require('@utils/response.utils');
  return ApiResponse.error(
    res,
    'Something went wrong',
    { field: 'name', message: 'Name is required' },
    400
  );
});

router.get('/test/unauthorized', (_, res) => {
  const { ApiResponse } = require('@utils/response.utils');
  return ApiResponse.error(
    res,
    'You are not authorized to access this resource',
    null,
    401
  );
});

/**
 * Export router
 */
module.exports = router;
