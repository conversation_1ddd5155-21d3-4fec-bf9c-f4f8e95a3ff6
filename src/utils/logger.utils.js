/**
 * Logger Service
 *
 * Provides logging functionality
 */
const chalk = require('chalk');

/**
 * Logger service
 */
const Logger = {
  /**
   * Initialize logger
   */
  init: () => {
    // Set up any logger configuration here
    console.log('Logger initialized');
  },

  /**
   * Log info message
   * @param {string} message - Message to log
   * @param {*} data - Additional data to log
   */
  info: (message, data = null) => {
    console.log(chalk.blue('[INFO]'), message);

    if (data) {
      console.log(data);
    }
  },

  /**
   * Log success message
   * @param {string} message - Message to log
   * @param {*} data - Additional data to log
   */
  success: (message, data = null) => {
    console.log(chalk.green('[SUCCESS]'), message);

    if (data) {
      console.log(data);
    }
  },

  /**
   * Log warning message
   * @param {string} message - Message to log
   * @param {*} data - Additional data to log
   */
  warn: (message, data = null) => {
    console.log(chalk.yellow('[WARN]'), message);

    if (data) {
      console.log(data);
    }
  },

  /**
   * Log error message
   * @param {string} message - Message to log
   * @param {*} error - Error object or additional data
   */
  error: (message, error = null) => {
    console.error(chalk.red('[ERROR]'), message);

    if (error) {
      if (error instanceof Error) {
        console.error(error.stack);
      } else {
        console.error(error);
      }
    }
  },

  /**
   * Log debug message (only in development)
   * @param {string} message - Message to log
   * @param {*} data - Additional data to log
   */
  debug: (message, data = null) => {
    if (process.env.NODE_ENV !== 'production') {
      console.log(chalk.magenta('[DEBUG]'), message);

      if (data) {
        console.log(data);
      }
    }
  },
};

module.exports = Logger;
