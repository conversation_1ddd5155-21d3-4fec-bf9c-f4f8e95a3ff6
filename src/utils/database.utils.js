/**
 * Database utility functions
 */
const { Sequelize } = require('sequelize');
const config = require('@config/app.config');
const Logger = require('@utils/logger.utils');

/**
 * Get database configuration for the current environment
 * @returns {Object} Database configuration
 */
function getDatabaseConfig() {
  const env = process.env.NODE_ENV || 'development';
  return config[env] || config.development;
}

/**
 * Create a Sequelize instance for the current environment
 * @returns {Sequelize} Sequelize instance
 */
function createSequelizeInstance() {
  const dbConfig = getDatabaseConfig();
  const env = process.env.NODE_ENV || 'development';

  Logger.info(`Creating Sequelize instance for environment: ${env}`);

  let sequelize;
  if (dbConfig.use_env_variable) {
    const connectionString = process.env[dbConfig.use_env_variable];

    if (!connectionString) {
      Logger.error(`Environment variable ${dbConfig.use_env_variable} not set`);
      throw new Error(
        `Environment variable ${dbConfig.use_env_variable} not set`
      );
    }

    Logger.info(`Connecting to database using ${dbConfig.use_env_variable}`);
    sequelize = new Sequelize(connectionString, dbConfig);
  } else {
    Logger.info('Connecting to database using direct configuration');
    sequelize = new Sequelize(
      dbConfig.database,
      dbConfig.username,
      dbConfig.password,
      dbConfig
    );
  }

  return sequelize;
}

/**
 * Test database connection
 * @param {Sequelize} sequelize - Sequelize instance
 * @returns {Promise<boolean>} Connection status
 */
async function testConnection(sequelize) {
  try {
    await sequelize.authenticate();
    Logger.success('Database connection established successfully');
    return true;
  } catch (error) {
    Logger.error('Unable to connect to the database', error);
    return false;
  }
}

/**
 * Get database URL for the current environment
 * @returns {string|null} Database URL
 */
function getDatabaseUrl() {
  const dbConfig = getDatabaseConfig();

  if (dbConfig.use_env_variable) {
    return process.env[dbConfig.use_env_variable] || null;
  }

  return null;
}

/**
 * Get current environment
 * @returns {string} Current environment
 */
function getCurrentEnvironment() {
  return process.env.NODE_ENV || 'development';
}

module.exports = {
  getDatabaseConfig,
  createSequelizeInstance,
  testConnection,
  getDatabaseUrl,
  getCurrentEnvironment,
};
