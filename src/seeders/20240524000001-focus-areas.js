'use strict';
const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if focus areas already exist
    const existingFocusAreas = await queryInterface.sequelize.query(
      'SELECT LOWER(name) as lowercase_name FROM "Focus"',
      { type: Sequelize.QueryTypes.SELECT }
    );

    const existingLowercaseNames = existingFocusAreas.map(
      (focus) => focus.lowercase_name
    );

    // Define focus areas data with proper capitalization and different timestamps
    // Create a base date and then add different offsets for each record
    const baseDate = new Date();

    const focusAreas = [
      {
        id: uuidv4(),
        name: 'Early Learning',
        createdAt: new Date(baseDate.getTime()),
        updatedAt: new Date(baseDate.getTime()),
      },
      {
        id: uuidv4(),
        name: 'Elementary',
        createdAt: new Date(baseDate.getTime() + 1000), // 1 second later
        updatedAt: new Date(baseDate.getTime() + 1000),
      },
      {
        id: uuidv4(),
        name: 'Secondary',
        createdAt: new Date(baseDate.getTime() + 2000), // 2 seconds later
        updatedAt: new Date(baseDate.getTime() + 2000),
      },
    ];

    // Filter out focus areas that already exist (case-insensitive comparison)
    // but preserve the original case when inserting
    const newFocusAreas = focusAreas.filter(
      (focus) => !existingLowercaseNames.includes(focus.name.toLowerCase())
    );

    if (newFocusAreas.length > 0) {
      await queryInterface.bulkInsert('Focus', newFocusAreas);
      console.log(`${newFocusAreas.length} focus areas created successfully`);
    } else {
      console.log('All focus areas already exist, skipping creation');
    }
  },

  async down(queryInterface, Sequelize) {
    // Define the names of the focus areas to be removed
    const names = ['Early Learning', 'Elementary', 'Secondary'];

    // Delete the focus areas (case-insensitive)
    for (const name of names) {
      await queryInterface.sequelize.query(
        `
        DELETE FROM "Focus"
        WHERE LOWER(name) = LOWER(:name)
      `,
        {
          replacements: { name },
          type: Sequelize.QueryTypes.DELETE,
        }
      );
    }

    console.log('Focus areas removed successfully');
  },
};
