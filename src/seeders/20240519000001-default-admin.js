'use strict';
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if admin with this email already exists
    const existingAdmin = await queryInterface.sequelize.query(
      'SELECT * FROM "Admin" WHERE email = :email',
      {
        replacements: { email: '<EMAIL>' },
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    // Only insert if admin doesn't exist
    if (existingAdmin.length === 0) {
      // Hash the password
      const hashedPassword = await bcrypt.hash('Qwerty@123', 10);

      // Create default admin
      await queryInterface.bulkInsert(
        'Admin',
        [
          {
            id: uuidv4(),
            email: '<EMAIL>',
            password: hashedPassword,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        {}
      );

      console.log('Default admin created successfully');
    } else {
      console.log('Default admin already exists, skipping creation');
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove the default admin
    await queryInterface.bulkDelete('Admin', { email: '<EMAIL>' }, {});
  },
};
