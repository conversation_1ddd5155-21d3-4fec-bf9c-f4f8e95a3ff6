'use strict';
const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get all users
    const users = await queryInterface.sequelize.query(
      'SELECT id, email FROM "User"',
      { type: Sequelize.QueryTypes.SELECT }
    );

    // Get all focus areas
    const focusAreas = await queryInterface.sequelize.query(
      'SELECT id, name FROM "Focus"',
      { type: Sequelize.QueryTypes.SELECT }
    );

    // Get existing UserFocus associations
    const existingAssociations = await queryInterface.sequelize.query(
      'SELECT "userId", "focusId" FROM "UserFocus"',
      { type: Sequelize.QueryTypes.SELECT }
    );

    // Create a map of focus names to IDs
    const focusMap = focusAreas.reduce((acc, focus) => {
      acc[focus.name] = focus.id;
      return acc;
    }, {});

    // Create a map of user emails to IDs
    const userMap = users.reduce((acc, user) => {
      acc[user.email] = user.id;
      return acc;
    }, {});

    // Create a set of existing associations for quick lookup
    const existingSet = new Set(
      existingAssociations.map((assoc) => `${assoc.userId}-${assoc.focusId}`)
    );

    // Define user-focus associations based on the old focus field
    const userFocusAssociations = [
      // Educator users
      {
        email: '<EMAIL>',
        focus: 'Elementary',
      },
      {
        email: '<EMAIL>',
        focus: 'Early Learning',
      },
      {
        email: '<EMAIL>',
        focus: 'Secondary',
      },
      // Educator Plus users
      {
        email: '<EMAIL>',
        focus: 'Secondary',
      },
      {
        email: '<EMAIL>',
        focus: 'Elementary',
      },
      {
        email: '<EMAIL>',
        focus: 'Early Learning',
      },
      // Provider Plus users
      {
        email: '<EMAIL>',
        focus: 'Elementary',
      },
      {
        email: '<EMAIL>',
        focus: 'Secondary',
      },
      {
        email: '<EMAIL>',
        focus: 'Early Learning',
      },
    ];

    // Create UserFocus records, filtering out existing ones
    const userFocusRecords = userFocusAssociations
      .map(({ email, focus }) => {
        const userId = userMap[email];
        const focusId = focusMap[focus];
        const associationKey = `${userId}-${focusId}`;

        // Skip if this association already exists
        if (existingSet.has(associationKey)) {
          return null;
        }

        return {
          id: uuidv4(),
          userId,
          focusId,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      })
      .filter(Boolean); // Remove null entries

    if (userFocusRecords.length > 0) {
      await queryInterface.bulkInsert('UserFocus', userFocusRecords);
      console.log(
        `${userFocusRecords.length} new user-focus associations created`
      );
    } else {
      console.log(
        'All user-focus associations already exist, skipping creation'
      );
    }
  },

  async down(queryInterface, Sequelize) {
    // Get all users
    const users = await queryInterface.sequelize.query(
      'SELECT id FROM "User"',
      { type: Sequelize.QueryTypes.SELECT }
    );

    const userIds = users.map((user) => user.id);

    // Delete all UserFocus records for these users
    if (userIds.length > 0) {
      await queryInterface.bulkDelete('UserFocus', {
        userId: { [Sequelize.Op.in]: userIds },
      });
    }
  },
};
