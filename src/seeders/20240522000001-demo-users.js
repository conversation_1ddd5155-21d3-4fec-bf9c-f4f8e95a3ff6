'use strict';
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const { UserType } = require('../utils/enums.utils');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Hash the password once for all users
    const hashedPassword = await bcrypt.hash('Password@123', 10);

    // Check if users already exist
    const existingUsers = await queryInterface.sequelize.query(
      'SELECT email FROM "User"',
      { type: Sequelize.QueryTypes.SELECT }
    );

    const existingEmails = existingUsers.map((user) => user.email);

    // Define users data
    const users = [
      // Educator users
      {
        id: uuidv4(),
        firstName: 'Michael',
        lastName: 'Johnson',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: UserType.EDUCATOR,
        position: 'Superintendent',
        focus: 'Elementary',
        state: 'California',
        country: 'USA',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        firstName: 'Jennifer',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: UserType.EDUCATOR,
        position: 'Counselor',
        focus: 'Early Learning',
        state: 'Texas',
        country: 'USA',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        firstName: 'David',
        lastName: 'Brown',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: UserType.EDUCATOR,
        position: 'Teacher',
        focus: 'Secondary',
        state: 'New York',
        country: 'USA',
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // Educator Plus users
      {
        id: uuidv4(),
        firstName: 'Sarah',
        lastName: 'Miller',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: UserType.EDUCATOR_PLUS,
        position: 'Principal',
        focus: 'Secondary',
        state: 'Florida',
        country: 'USA',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        firstName: 'Robert',
        lastName: 'Davis',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: UserType.EDUCATOR_PLUS,
        position: 'Librarian',
        focus: 'Elementary',
        state: 'Illinois',
        country: 'USA',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        firstName: 'Emily',
        lastName: 'Garcia',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: UserType.EDUCATOR_PLUS,
        position: 'Support Staff',
        focus: 'Early Learning',
        state: 'Washington',
        country: 'USA',
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // Provider Plus users
      {
        id: uuidv4(),
        firstName: 'James',
        lastName: 'Wilson',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: UserType.PROVIDER_PLUS,
        companyName: 'Wilson Educational Services',
        focus: 'Elementary',
        state: 'Massachusetts',
        country: 'USA',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        firstName: 'Patricia',
        lastName: 'Martinez',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: UserType.PROVIDER_PLUS,
        companyName: 'Martinez Learning Solutions',
        focus: 'Secondary',
        state: 'Colorado',
        country: 'USA',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: uuidv4(),
        firstName: 'Thomas',
        lastName: 'Anderson',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: UserType.PROVIDER_PLUS,
        companyName: 'Anderson Educational Resources',
        focus: 'Early Learning',
        state: 'Virginia',
        country: 'USA',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    // Filter out users that already exist
    const newUsers = users.filter(
      (user) => !existingEmails.includes(user.email)
    );

    if (newUsers.length > 0) {
      await queryInterface.bulkInsert('User', newUsers);

      // Create memberships for Educator Plus and Provider Plus users
      const memberships = [];

      // Get the IDs of the newly inserted users
      const insertedUsers = await queryInterface.sequelize.query(
        'SELECT id, "userType" as "userType" FROM "User" WHERE email IN (:emails)',
        {
          replacements: { emails: newUsers.map((user) => user.email) },
          type: Sequelize.QueryTypes.SELECT,
        }
      );

      // Create memberships for premium users
      for (const user of insertedUsers) {
        if (
          user['userType'] === UserType.EDUCATOR_PLUS ||
          user['userType'] === UserType.PROVIDER_PLUS
        ) {
          // Calculate end date (1 year from now)
          const startDate = new Date();
          const endDate = new Date();
          endDate.setFullYear(endDate.getFullYear() + 1);

          memberships.push({
            id: uuidv4(),
            userId: user.id,
            membershipType: user['userType'],
            startDate,
            endDate,
            isActive: true,
            paymentReference: `SEED-${uuidv4().substring(0, 8)}`,
            amount:
              user['userType'] === UserType.EDUCATOR_PLUS ? 199.99 : 299.99,
            currency: 'USD',
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }
      }

      if (memberships.length > 0) {
        await queryInterface.bulkInsert('Membership', memberships);
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // Define the emails of the users to be removed
    const emails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    // Get the IDs of the users to be removed
    const users = await queryInterface.sequelize.query(
      'SELECT id FROM "User" WHERE email IN (:emails)',
      {
        replacements: { emails },
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const userIds = users.map((user) => user.id);

    // Delete memberships for these users
    if (userIds.length > 0) {
      await queryInterface.bulkDelete('Membership', {
        userId: { [Sequelize.Op.in]: userIds },
      });
    }

    // Delete the users
    await queryInterface.bulkDelete('User', {
      email: { [Sequelize.Op.in]: emails },
    });
  },
};
