/**
 * Role-based Access Control Middleware
 */
const { ApiException } = require('@utils/exception.utils');
const { AUTH } = require('@utils/messages.utils');
const { UserType } = require('@utils/enums.utils');

/**
 * Check if user has the required user type
 * @param {Array|string} requiredTypes - Required user type(s)
 * @returns {Function} Middleware function
 */
const requireUserType = (requiredTypes) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        throw new ApiException(401, AUTH.AUTHENTICATION_FAILED);
      }

      // Convert string type to array if needed
      const allowedTypes = Array.isArray(requiredTypes)
        ? requiredTypes
        : [requiredTypes];

      // Check if user's type is in the allowed types
      if (!allowedTypes.includes(req.user.userType)) {
        throw new ApiException(403, AUTH.UNAUTHORIZED);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Middleware to check if user is a provider plus
 */
const requireProviderPlus = requireUserType(UserType.PROVIDER_PLUS);

/**
 * Middleware to check if user is an educator plus
 */
const requireEducatorPlus = requireUserType(UserType.EDUCATOR_PLUS);

/**
 * Middleware to check if user is a premium user (educator plus or provider plus)
 */
const requirePremiumUser = requireUserType([
  UserType.EDUCATOR_PLUS,
  UserType.PROVIDER_PLUS,
]);

module.exports = {
  requireUserType,
  requireProviderPlus,
  requireEducatorPlus,
  requirePremiumUser,
};
