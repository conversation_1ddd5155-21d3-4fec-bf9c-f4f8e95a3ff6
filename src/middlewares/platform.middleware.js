/**
 * Platform Middleware
 */
const { PlatformType } = require('@config/app.config');
const { ApiException } = require('@utils/exception.utils');

/**
 * Middleware to determine platform type from headers
 */
const determinePlatform = (req, res, next) => {
  const appTypeHeader = req.headers['x-application-type'];

  if (!req.headers) {
    req.headers = {};
  }

  if (appTypeHeader) {
    req.platform = PlatformType.isValid(appTypeHeader.toLowerCase())
      ? appTypeHeader.toLowerCase()
      : PlatformType.WEB;
  } else {
    req.platform = PlatformType.WEB;
  }

  next();
};

/**
 * Check if the request is coming from a valid platform
 * @param {Array} allowedPlatforms - Array of allowed platforms
 */
const platformCheck = (allowedPlatforms = []) => {
  return (req, _, next) => {
    const platform = req.platform || req.headers['x-application-type'];

    if (!platform) {
      req.platform = PlatformType.WEB;
      return next();
    }

    if (!PlatformType.isValid(platform)) {
      return next(new ApiException(400, `Invalid platform type: ${platform}`));
    }

    if (allowedPlatforms.length > 0 && !allowedPlatforms.includes(platform)) {
      return next(
        new ApiException(
          403,
          `Platform ${platform} is not allowed to access this resource`
        )
      );
    }

    req.platform = platform;
    next();
  };
};

// Pre-configured middleware for common platform restrictions
const webOnly = platformCheck([PlatformType.WEB]);
const mobileOnly = platformCheck([PlatformType.MOBILE]);
const apiOnly = platformCheck([PlatformType.API]);
const webAndMobile = platformCheck([PlatformType.WEB, PlatformType.MOBILE]);

module.exports = {
  determinePlatform,
  platformCheck,
  webOnly,
  mobileOnly,
  apiOnly,
  webAndMobile,
};
