/**
 * Error Handling Middleware
 */
const { ApiResponse } = require('@utils/response.utils');
const Logger = require('@utils/logger.utils');
const { ApiException } = require('@utils/exception.utils');

/**
 * Error handler middleware
 */
const errorHandler = (err, _, res, next) => {
  Logger.error(`Error: ${err.message}`, err);

  // Handle ApiException
  if (err instanceof ApiException) {
    return ApiResponse.error(res, err.message, err.errors, err.statusCode);
  }

  // Handle Sequelize validation errors
  if (err.name === 'SequelizeValidationError') {
    return ApiResponse.error(
      res,
      'Validation error',
      err.errors.map((e) => ({ field: e.path, message: e.message })),
      400
    );
  }

  // Handle JWT errors
  if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    return ApiResponse.error(res, 'Authentication error', err.message, 401);
  }

  // Handle other errors
  const statusCode = err.statusCode || 500;
  const message =
    statusCode === 500 && process.env.NODE_ENV === 'production'
      ? 'Internal server error'
      : err.message;

  return ApiResponse.error(
    res,
    message,
    process.env.NODE_ENV === 'production' ? null : err.stack,
    statusCode
  );
};

/**
 * Not found error handler
 */
const notFoundHandler = (req, _, next) => {
  next(new ApiException(404, `The requested resource was not found`));
};

module.exports = {
  errorHandler,
  notFoundHandler,
};
