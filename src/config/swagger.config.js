const path = require('path');
const { APP } = require('./app.config');
const swaggerJSDoc = require('swagger-jsdoc');

const swaggerOptions = {
  swaggerDefinition: {
    openapi: '3.0.0',
    info: {
      title: 'WTD Platform API Documentation',
      version: '1.0.0',
      description: 'WTD Platform API Documentation',
    },
    servers: [
      {
        url: `http://localhost:${APP.port}`,
        description: 'Local Development server',
      },
      {
        url: 'https://dev-api.wtdplatform.com',
        description: 'Development server',
      },
      {
        url: 'https://qa-api.wtdplatform.com',
        description: 'QA server',
      },
    ],
    components: {
      securitySchemes: {
        BearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
  },
  apis: [
    path.join(__dirname, '../api-docs/**/*.yaml'),
    path.join(__dirname, '../api-docs/*.yaml'),
  ],
  failOnErrors: true,
};

// Admin Swagger configuration
const adminOptions = {
  ...swaggerOptions,
  swaggerDefinition: {
    ...swaggerOptions.swaggerDefinition,
    info: {
      title: 'WTD Platform Admin API Documentation',
      version: '1.0.0',
      description: 'WTD Platform Admin API Documentation',
    },
  },
  apis: [path.join(__dirname, '../api-docs/admin/**/*.yaml')],
};

// User Swagger configuration
const userOptions = {
  ...swaggerOptions,
  swaggerDefinition: {
    ...swaggerOptions.swaggerDefinition,
    info: {
      title: 'WTD Platform User API Documentation',
      version: '1.0.0',
      description: 'WTD Platform User API Documentation',
    },
  },
  apis: [path.join(__dirname, '../api-docs/users/**/*.yaml')],
};

const adminSpec = swaggerJSDoc(adminOptions);
const userSpec = swaggerJSDoc(userOptions);

module.exports = {
  adminSpec,
  userSpec,
};
