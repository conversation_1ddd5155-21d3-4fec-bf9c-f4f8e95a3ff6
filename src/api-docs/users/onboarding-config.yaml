openapi: 3.0.0
info:
  title: WTD Platform User Onboarding Config API
  version: 1.0.0
  description: API endpoints for accessing onboarding configurations as a user

paths:
  /user/onboarding-config:
    get:
      tags:
        - User Onboarding Config
      summary: List All Onboarding Configs
      description: Get a list of all onboarding configs with pagination
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          description: Successfully retrieved onboarding configs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConfigListResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/onboarding-config/{id}:
    get:
      tags:
        - User Onboarding Config
      summary: Get Onboarding Config by ID
      description: Get a specific onboarding config by its ID
      parameters:
        - $ref: '#/components/parameters/OnboardingConfigIdParam'
      responses:
        '200':
          description: Successfully retrieved onboarding config
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConfigResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Onboarding config not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    OnboardingConfigIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Onboarding config ID

  schemas:
    OnboardingConfig:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the onboarding config
          example: "123e4567-e89b-12d3-a456-************"
        title:
          type: string
          description: Title of the onboarding screen
          example: "Welcome to WTD Platform"
        description:
          type: string
          description: Description text for the onboarding screen
          example: "Learn how to get started with our platform"
        imageUrl:
          type: string
          format: uri
          description: URL of the image to display
          example: "https://example.com/images/welcome.png"
        createdAt:
          type: string
          format: date-time
          description: Date and time when the config was created
          example: "2024-03-20T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the config was last updated
          example: "2024-03-20T10:00:00Z"
      required:
        - id
        - title
        - description
        - createdAt
        - updatedAt

    OnboardingConfigResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Onboarding config retrieved successfully"
        data:
          $ref: '#/components/schemas/OnboardingConfig'

    OnboardingConfigListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Onboarding configs retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/OnboardingConfig'
        meta:
          type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 12
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 2
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: true
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message"
        errors:
          type: array
          items:
            type: object
            properties:
              param:
                type: string
                example: "id"
              msg:
                type: string
                example: "Invalid ID format"
              location:
                type: string
                example: "params" 