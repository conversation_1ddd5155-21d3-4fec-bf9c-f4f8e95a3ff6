openapi: 3.0.0
info:
  title: WTD Platform User PD Categories API
  version: 1.0.0
  description: API endpoints for users to view PD categories

paths:
  /user/pd-categories:
    get:
      tags:
        - PD Categories
      summary: List All PD Categories
      description: Get a list of all PD categories with pagination and optional search
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
      responses:
        '200':
          description: Successfully retrieved PD categories
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PdCategoryListResponse'

  /user/pd-categories/{id}:
    get:
      tags:
        - PD Categories
      summary: Get PD Category by ID
      description: Get a specific PD category by its ID
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PdCategoryIdParam'
      responses:
        '200':
          description: Successfully retrieved PD category
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PdCategoryResponse'
        '404':
          description: PD category not found

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    SearchParam:
      name: search
      in: query
      schema:
        type: string
      description: Optional search term to filter categories by name

    PdCategoryIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: PD category ID
  schemas:
    PdCategory:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the PD category
        name:
          type: string
          description: Name of the PD category
        createdAt:
          type: string
          format: date-time
          description: Date and time when the category was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the category was last updated
      required:
        - id
        - name
        - createdAt
        - updatedAt
      example:
        id: "123e4567-e89b-12d3-a456-************"
        name: "Student Engagement"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    PdCategoryResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "PD category retrieved successfully"
        data:
          $ref: '#/components/schemas/PdCategory'

    PdCategoryListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "All PD categories retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/PdCategory'
        pagination:
          $ref: '#/components/schemas/Pagination'

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 12
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 2
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: true
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false
