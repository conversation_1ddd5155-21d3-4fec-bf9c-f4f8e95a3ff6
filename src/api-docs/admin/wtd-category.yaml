openapi: 3.0.0
info:
  title: WTD Platform WTD Categories API
  version: 1.0.0
  description: API endpoints for managing WTD categories

paths:
  /admin/wtd-categories:
    get:
      tags:
        - WTD Categories
      summary: List All WTD Categories
      description: Get a list of all WTD categories with pagination and optional search
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
      responses:
        '200':
          description: Successfully retrieved WTD categories
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WtdCategoryListResponse'
    post:
      tags:
        - WTD Categories
      summary: Create WTD Category
      description: Create a new WTD category
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWtdCategoryRequest'
      responses:
        '201':
          description: Successfully created WTD category
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WtdCategoryResponse'
        '409':
          description: A category with this name already exists

  /admin/wtd-categories/{id}:
    get:
      tags:
        - WTD Categories
      summary: Get WTD Category by ID
      description: Get a specific WTD category by its ID
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/WtdCategoryIdParam'
      responses:
        '200':
          description: Successfully retrieved WTD category
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WtdCategoryResponse'
        '404':
          description: WTD category not found
    put:
      tags:
        - WTD Categories
      summary: Update WTD Category
      description: Update an existing WTD category
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/WtdCategoryIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWtdCategoryRequest'
      responses:
        '200':
          description: Successfully updated WTD category
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WtdCategoryResponse'
        '404':
          description: WTD category not found
        '409':
          description: Another category with this name already exists
    delete:
      tags:
        - WTD Categories
      summary: Delete WTD Category
      description: Delete an existing WTD category
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/WtdCategoryIdParam'
      responses:
        '200':
          description: Successfully deleted WTD category
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteResponse'
        '404':
          description: WTD category not found

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    SearchParam:
      name: search
      in: query
      schema:
        type: string
      description: Optional search term to filter categories by name

    WtdCategoryIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: WTD category ID

  schemas:
    WtdCategory:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the WTD category
        name:
          type: string
          description: Name of the WTD category
        createdAt:
          type: string
          format: date-time
          description: Date and time when the category was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the category was last updated
      required:
        - id
        - name
        - createdAt
        - updatedAt
      example:
        id: "123e4567-e89b-12d3-a456-************"
        name: "Category 1"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    CreateWtdCategoryRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "Category 1"

    UpdateWtdCategoryRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "Updated Category Name"

    WtdCategoryResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "WTD category created successfully"
        data:
          $ref: '#/components/schemas/WtdCategory'

    WtdCategoryListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/WtdCategory'
        pagination:
          $ref: '#/components/schemas/Pagination'

    DeleteResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "WTD category deleted successfully"

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 12
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 2
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: true
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false
