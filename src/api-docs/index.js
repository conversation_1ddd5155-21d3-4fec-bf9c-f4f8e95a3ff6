const router = require('express').Router();
const basicAuth = require('express-basic-auth');
const admin = require('./admin');
const users = require('./users');

// Default credentials if environment variables are not set
const swaggerUser = process.env.SWAGGER_DOCS_USER || 'api_docs';
const swaggerPassword = process.env.SWAGGER_DOCS_PASSWORD || 'ApiDocs@2025';

router.use(
  '/admin',
  basicAuth({
    users: {
      [swaggerUser]: swaggerPassword,
    },
    challenge: true,
  }),
  admin
);

router.use(
  '/user',
  basicAuth({
    users: {
      [swaggerUser]: swaggerPassword,
    },
    challenge: true,
  }),
  users
);

module.exports = router;
