# Dependency directories
node_modules/
jspm_packages/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Coverage directory used by tools like istanbul
coverage
*.lcov
.nyc_output

# Compiled binary addons
build/Release
dist/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Yarn and NPM lock files (use one package manager)
yarn.lock
package-lock.json

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.swp
*~
*.seed
*.dat
*.out
*.pid
*.gz

# IDE files
.idea/
.vscode/
*.iml
*.ipr
modules.xml

# OS files
.DS_Store
Thumbs.db
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
Desktop.ini
$RECYCLE.BIN/

# Project specific
uploads/
attachments/
create_admin.js